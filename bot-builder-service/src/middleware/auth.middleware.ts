import { Request, Response, NextFunction } from "express";
import { ApiResponse } from "@neuratalk/common";
import { logger } from "@neuratalk/common";

declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        [key: string]: any;
      };
    }
  }
}

export function authMiddleware(req: Request, res: Response, next: NextFunction): void {
  try {
    // For now, mock user ID - in production this would extract from JWT token
    const userId = (req.headers["x-user-id"] as string) || "system-user";

    req.user = {
      id: userId,
    };

    next();
  } catch (error) {
    logger.error("Error in auth middleware:", error);
    res.status(500).json({
      success: false,
      error: {
        code: "AUTH_ERROR",
        message: "Authentication failed",
      },
      timestamp: new Date(),
    } as ApiResponse);
  }
}
