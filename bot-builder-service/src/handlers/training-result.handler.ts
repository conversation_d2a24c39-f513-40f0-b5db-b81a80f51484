import { EachMessagePayload } from 'kafkajs';
import { logger } from '@neuratalk/common';
import { BuildService } from '../services/build.service';
import { TrainingResultStatus } from '../types/build-bot.type';


interface TrainingResultMessage {
  jobId: string;
  botId: string;
  status: TrainingResultStatus;
  modelUrl?: string;
  errorMessage?: string;
}

export class TrainingResultHandler {
  private buildService: BuildService;

  constructor(buildService: BuildService) {
    this.buildService = buildService;
  }

  public handleMessage = async (payload: EachMessagePayload): Promise<void> => {
    try {
      const message: TrainingResultMessage = JSON.parse(
        payload.message.value!.toString(),
      );
      logger.info('Received training result:', { message });

      await this.buildService.updateBuildJob(message.jobId, {
        status: message.status,
        modelUrl: message.modelUrl,
        errorMessage: message.errorMessage,
      });
    } catch (error) {
      logger.error('Error processing training result message:', {
        error,
        value: payload.message.value?.toString(),
      });
    }
  };
}