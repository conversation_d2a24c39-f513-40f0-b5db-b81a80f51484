import { Router } from "express";
import { Models } from "@neuratalk/bot-store";
import { createKnowledgeRoutes } from "./knowledge.routes";
import { createBuildRoutes } from "./build.router"; 
import { createAppRoutes } from "./app.router";   
import { BuildController } from "../controllers/build.controller";


interface ControllerDependencies {
  buildController: BuildController;
  models: Models; 
}

export function createRoutes(
  dependencies: ControllerDependencies
): Router[] {

  const appRouter = createAppRoutes();
  const buildRouter = createBuildRoutes(dependencies.buildController);
  const knowledgeRouter = createKnowledgeRoutes(dependencies.models);
  

  return [appRouter, buildRouter, knowledgeRouter];
}