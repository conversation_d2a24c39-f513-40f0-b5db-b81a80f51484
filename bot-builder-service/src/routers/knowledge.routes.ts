import { Router } from "express";
import { KnowledgeUnitController } from "../controllers/knowledge-unit.controller";
import { FaqItemsController } from "../controllers/faq-items.controller";
import { IntentItemsController } from "../controllers/intent-items.controller";
import { EntitiesController } from "../controllers/entities.controller";
import { Models } from "@neuratalk/bot-store";
import { authMiddleware } from "../middleware/auth.middleware";
import {
  PaginationQuerySchema,
  UuidParamSchema,
  validateBody,
  validateParams,
  validateQuery,
} from "@neuratalk/common";
import {
  CreateKnowledgeUnitSchema,
  UpdateKnowledgeUnitSchema,
  CreateFaqItemSchema,
  UpdateFaqItemSchema,
  CreateIntentItemSchema,
  UpdateIntentItemSchema,
  CreateEntitySchema,
  UpdateEntitySchema,
} from "../schemas";

export function createKnowledgeRoutes(models: Models): Router {
  const router = Router();

  const knowledgeUnitController = new KnowledgeUnitController(models);
  const faqItemsController = new FaqItemsController(models);
  const intentItemsController = new IntentItemsController(models);
  const entitiesController = new EntitiesController(models);

  // Knowledge Unit routes
  router.post(
    "/knowledge-units",
    authMiddleware,
    validateBody(CreateKnowledgeUnitSchema),
    knowledgeUnitController.create.bind(knowledgeUnitController),
  );
  router.get(
    "/knowledge-units",
    validateQuery(PaginationQuerySchema),
    knowledgeUnitController.getAll.bind(knowledgeUnitController), //TODO: rm bind here
  );
  router.get(
    "/knowledge-units/:id",
    validateParams(UuidParamSchema),
    knowledgeUnitController.getById.bind(knowledgeUnitController),
  );
  router.put(
    "/knowledge-units/:id",
    authMiddleware,
    validateParams(UuidParamSchema),
    validateBody(UpdateKnowledgeUnitSchema),
    knowledgeUnitController.update.bind(knowledgeUnitController),
  );
  router.delete(
    "/knowledge-units/:id",
    validateParams(UuidParamSchema),
    knowledgeUnitController.delete.bind(knowledgeUnitController),
  );

  // FAQ Items routes
  router.post(
    "/faq-items",
    authMiddleware,
    validateBody(CreateFaqItemSchema),
    faqItemsController.create.bind(faqItemsController),
  );
  router.get("/faq-items", validateQuery(PaginationQuerySchema), faqItemsController.getAll);
  router.get(
    "/faq-items/:id",
    validateParams(UuidParamSchema),
    faqItemsController.getById.bind(faqItemsController),
  );
  router.put(
    "/faq-items/:id",
    authMiddleware,
    validateParams(UuidParamSchema),
    validateBody(UpdateFaqItemSchema),
    faqItemsController.update.bind(faqItemsController),
  );
  router.delete(
    "/faq-items/:id",
    validateParams(UuidParamSchema),
    faqItemsController.delete.bind(faqItemsController),
  );

  // Intent Items routes
  router.post(
    "/intent-items",
    authMiddleware,
    validateBody(CreateIntentItemSchema),
    intentItemsController.create.bind(intentItemsController),
  );
  router.get(
    "/intent-items",
    validateQuery(PaginationQuerySchema),
    intentItemsController.getAll.bind(intentItemsController),
  );
  router.get(
    "/intent-items/:id",
    validateParams(UuidParamSchema),
    intentItemsController.getById.bind(intentItemsController),
  );
  router.put(
    "/intent-items/:id",
    authMiddleware,
    validateParams(UuidParamSchema),
    validateBody(UpdateIntentItemSchema),
    intentItemsController.update.bind(intentItemsController),
  );
  router.delete(
    "/intent-items/:id",
    validateParams(UuidParamSchema),
    intentItemsController.delete.bind(intentItemsController),
  );

  // Entities routes
  router.post(
    "/entities",
    authMiddleware,
    validateBody(CreateEntitySchema),
    entitiesController.create.bind(entitiesController),
  );
  router.get(
    "/entities",
    validateQuery(PaginationQuerySchema),
    entitiesController.getAll.bind(entitiesController),
  );
  router.get(
    "/entities/:id",
    validateParams(UuidParamSchema),
    entitiesController.getById.bind(entitiesController),
  );
  router.put(
    "/entities/:id",
    authMiddleware,
    validateParams(UuidParamSchema),
    validateBody(UpdateEntitySchema),
    entitiesController.update.bind(entitiesController),
  );
  router.delete(
    "/entities/:id",
    validateParams(UuidParamSchema),
    entitiesController.delete.bind(entitiesController),
  );

  return router;
}
