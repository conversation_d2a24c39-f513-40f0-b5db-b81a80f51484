import { Request, Response } from "express";
import { KnowledgeUnitController } from "../controllers/knowledge-unit.controller";

// Mock functions
jest.mock("@neuratalk/common", () => ({
  getPaginatedResults: jest.fn(),
  logger: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
  },
}));

const { getPaginatedResults } = require("@neuratalk/common");

describe("KnowledgeUnitController", () => {
  let controller: KnowledgeUnitController;
  let mockModels: any;
  let mockReq: Partial<Request>;
  let mockRes: Partial<Response>;

  beforeEach(() => {
    mockModels = {
      KnowledgeUnit: {
        create: jest.fn(),
        findOne: jest.fn(),
        findByPk: jest.fn(),
        update: jest.fn(),
        destroy: jest.fn(),
      },
    };

    controller = new KnowledgeUnitController(mockModels);

    mockReq = {
      body: {},
      params: {},
      query: {},
      user: { id: "user-123" },
    };

    mockRes = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockReturnThis(),
      send: jest.fn().mockReturnThis(),
    };

    jest.clearAllMocks();
  });

  describe("create", () => {
    it("should create knowledge unit successfully", async () => {
      const mockKnowledgeUnit = { id: "ku-123", name: "Test KU" };
      mockReq.body = { name: "Test KU", type: "faq", metadata: {} };
      mockModels.KnowledgeUnit.create.mockResolvedValue(mockKnowledgeUnit);

      await controller.create(mockReq as any, mockRes as Response);

      expect(mockModels.KnowledgeUnit.create).toHaveBeenCalledWith({
        name: "Test KU",
        type: "faq",
        metadata: {},
        createdBy: "user-123",
        updatedBy: "user-123",
      });
      expect(mockRes.status).toHaveBeenCalledWith(201);
    });

    it("should handle creation error", async () => {
      mockReq.body = { name: "Test KU", type: "faq" };
      mockModels.KnowledgeUnit.create.mockRejectedValue(new Error("Creation failed"));

      await controller.create(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(400);
    });
  });

  describe("getAll", () => {
    it("should get paginated knowledge units", async () => {
      const mockResult = {
        items: [{ id: "ku-1" }, { id: "ku-2" }],
        pagination: { page: 1, limit: 20, total: 2, totalPages: 1, hasNext: false, hasPrev: false },
      };
      mockReq.query = { page: "1", limit: "20" };
      (getPaginatedResults as jest.Mock).mockResolvedValue(mockResult);

      await controller.getAll(mockReq as any, mockRes as Response);

      expect(getPaginatedResults).toHaveBeenCalledWith(mockModels.KnowledgeUnit, mockReq.query, [
        "name",
      ]);
      expect(mockRes.json).toHaveBeenCalledWith({
        success: true,
        data: mockResult,
        timestamp: expect.any(Date),
      });
    });

    it("should handle getAll error", async () => {
      mockReq.query = { page: "1", limit: "20" };
      (getPaginatedResults as jest.Mock).mockRejectedValue(new Error("Database error"));

      await controller.getAll(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(500);
    });
  });

  describe("getById", () => {
    it("should get knowledge unit by id", async () => {
      const mockKnowledgeUnit = { id: "ku-123", name: "Test KU" };
      mockReq.params = { id: "ku-123" };
      mockModels.KnowledgeUnit.findOne.mockResolvedValue(mockKnowledgeUnit);

      await controller.getById(mockReq as any, mockRes as Response);

      expect(mockModels.KnowledgeUnit.findOne).toHaveBeenCalledWith({
        where: { id: "ku-123", deletedAt: null },
      });
      expect(mockRes.json).toHaveBeenCalledWith({
        success: true,
        data: mockKnowledgeUnit,
        timestamp: expect.any(Date),
      });
    });

    it("should return 404 when knowledge unit not found", async () => {
      mockReq.params = { id: "ku-123" };
      mockModels.KnowledgeUnit.findOne.mockResolvedValue(null);

      await controller.getById(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(404);
    });

    it("should handle getById error", async () => {
      mockReq.params = { id: "ku-123" };
      mockModels.KnowledgeUnit.findOne.mockRejectedValue(new Error("Database error"));

      await controller.getById(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(500);
    });
  });

  describe("update", () => {
    it("should update knowledge unit successfully", async () => {
      const mockUpdatedKU = { id: "ku-123", name: "Updated KU" };
      mockReq.params = { id: "ku-123" };
      mockReq.body = { name: "Updated KU" };
      mockModels.KnowledgeUnit.update.mockResolvedValue([1]);
      mockModels.KnowledgeUnit.findByPk.mockResolvedValue(mockUpdatedKU);

      await controller.update(mockReq as any, mockRes as Response);

      expect(mockModels.KnowledgeUnit.update).toHaveBeenCalledWith(
        { name: "Updated KU", updatedBy: "user-123" },
        { where: { id: "ku-123", deletedAt: null } },
      );
      expect(mockRes.json).toHaveBeenCalledWith({
        success: true,
        data: mockUpdatedKU,
        timestamp: expect.any(Date),
      });
    });

    it("should return 404 when updating non-existent knowledge unit", async () => {
      mockReq.params = { id: "ku-123" };
      mockReq.body = { name: "Updated KU" };
      mockModels.KnowledgeUnit.update.mockResolvedValue([0]);

      await controller.update(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(404);
    });

    it("should handle update error", async () => {
      mockReq.params = { id: "ku-123" };
      mockReq.body = { name: "Updated KU" };
      mockModels.KnowledgeUnit.update.mockRejectedValue(new Error("Update failed"));

      await controller.update(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(400);
    });
  });

  describe("delete", () => {
    it("should delete knowledge unit successfully", async () => {
      mockReq.params = { id: "ku-123" };
      mockModels.KnowledgeUnit.destroy.mockResolvedValue(1);

      await controller.delete(mockReq as any, mockRes as Response);

      expect(mockModels.KnowledgeUnit.destroy).toHaveBeenCalledWith({
        where: { id: "ku-123" },
      });
      expect(mockRes.status).toHaveBeenCalledWith(204);
    });

    it("should return 404 when deleting non-existent knowledge unit", async () => {
      mockReq.params = { id: "ku-123" };
      mockModels.KnowledgeUnit.destroy.mockResolvedValue(0);

      await controller.delete(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(404);
    });

    it("should handle delete error", async () => {
      mockReq.params = { id: "ku-123" };
      mockModels.KnowledgeUnit.destroy.mockRejectedValue(new Error("Delete failed"));

      await controller.delete(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(500);
    });
  });
});
