import { z } from "zod";
import { UuidSchema, PaginationQuerySchema } from "@neuratalk/common";

/**
 * Schema for route parameter containing an App ID.
 */
export const AppIdParamSchema = z.object({
  appId: UuidSchema,
});

/**
 * Schema for creating a new App.
 * Based on the model definition and common use cases.
 */
export const CreateAppSchema = z.object({
  name: z.string().min(1, "Name is required").max(32, "Name must be 32 characters or less"),
  desc: z.string().max(128, "Description must be 128 characters or less").optional(),
  // `appData` can be a complex object, so we'll be flexible.
  appData: z.record(z.any()).optional(),
  // Other potential fields from the model that might be set on creation.
  alignment: z.string().optional(),
  svg: z.string().optional(),
  OTC: z.string().optional(),
  MRC: z.string().optional(),
});

/**
 * Schema for updating an existing App. All fields are optional.
 */
export const UpdateAppSchema = CreateAppSchema.partial();

/**
 * Schema for querying a list of Apps with pagination and filtering.
 */
export const GetAppsQuerySchema = PaginationQuerySchema.extend({
  // The status codes are integers in the db but passed as strings in query params.
  status: z.string().optional(),
  user: z.string().optional(), // Assuming user is a filterable field.
});

// Type extraction for type-safe usage in controllers and services.
export type CreateAppRequest = z.infer<typeof CreateAppSchema>;
export type UpdateAppRequest = z.infer<typeof UpdateAppSchema>;
export type AppIdParam = z.infer<typeof AppIdParamSchema>;
export type GetAppsQuery = z.infer<typeof GetAppsQuerySchema>;