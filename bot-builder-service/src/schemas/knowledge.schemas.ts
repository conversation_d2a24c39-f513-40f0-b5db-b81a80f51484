import { z } from "zod";
import { UuidSchema } from "@neuratalk/common";

// Knowledge Unit schemas
export const KnowledgeTypeSchema = z.enum(["faq", "intent", "small_talk"]);

export const CreateKnowledgeUnitSchema = z.object({
  botId: UuidSchema,
  name: z.string().min(1).max(255),
  type: KnowledgeTypeSchema,
  metadata: z.record(z.any()).default({}),
  flowId: UuidSchema.optional(),
});

export const UpdateKnowledgeUnitSchema = CreateKnowledgeUnitSchema.partial();

// FAQ Item schemas
export const CreateFaqItemSchema = z.object({
  knowledgeUnitId: UuidSchema,
  questions: z.array(z.string().min(1)).min(1),
  answer: z.string().min(1),
});

export const UpdateFaqItemSchema = CreateFaqItemSchema.partial();

// Intent Item schemas
export const CreateIntentItemSchema = z.object({
  knowledgeUnitId: UuidSchema,
  text: z.string().min(1),
  entities: z
    .array(
      z.object({
        id: UuidSchema,
        name: z.string().min(1),
        value: z.string().min(1),
      }),
    )
    .default([]),
});

export const UpdateIntentItemSchema = CreateIntentItemSchema.partial();

// Entity schemas
export const CreateEntitySchema = z.object({
  name: z.string().min(1).max(100),
  knowledgeUnitId: UuidSchema.optional(),
});

export const UpdateEntitySchema = CreateEntitySchema.partial();

// Type extraction
export type CreateKnowledgeUnitRequest = z.infer<typeof CreateKnowledgeUnitSchema>;
export type UpdateKnowledgeUnitRequest = z.infer<typeof UpdateKnowledgeUnitSchema>;
export type CreateFaqItemRequest = z.infer<typeof CreateFaqItemSchema>;
export type UpdateFaqItemRequest = z.infer<typeof UpdateFaqItemSchema>;
export type CreateIntentItemRequest = z.infer<typeof CreateIntentItemSchema>;
export type UpdateIntentItemRequest = z.infer<typeof UpdateIntentItemSchema>;
export type CreateEntityRequest = z.infer<typeof CreateEntitySchema>;
export type UpdateEntityRequest = z.infer<typeof UpdateEntitySchema>;
