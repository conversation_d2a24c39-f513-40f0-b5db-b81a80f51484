/**
 * Bot Builder Service Application
 *
 * Main application setup for the bot-builder-service.
 */

import express, { Application, Request, Response, NextFunction, Router } from "express";
import cors from "cors";
import helmet from "helmet";
import compression from "compression";
import morgan from "morgan";
import swaggerUi from "swagger-ui-express";
import swaggerSpec from "./config/swagger";

import { TrainingResultHandler } from "./handlers/training-result.handler";

import { BotService } from "./services/bot.service";
import { FlowService } from "./services/flow.service";
import { RasaService } from "./services/rasa.service";
import { BotController } from "./controllers/bot.controller";
import { FlowController } from "./controllers/flow.controller";
import config from "./config";
import { DatabaseConnection } from "@neuratalk/bot-store";
import {
  ApiResponse,
  logger,
  KafkaProducer,
  KafkaConsumer,
  validate<PERSON>uery,
  PaginationQuerySchema,
} from "@neuratalk/common";
import { createRoutes, RouteDependencies } from "./routers/index.router";
import { BuildController } from "./controllers/build.controller";
import { BuildService } from "./services/build.service";
import { AppController } from "./controllers/app.controller";

export class App {
  public app: Application;
  private db: DatabaseConnection;
  private botService!: BotService;
  private flowService!: FlowService;
  private botController!: BotController;
  private flowController!: FlowController;
  private appController!: AppController;
  private buildController!: BuildController;
  private buildService!: BuildService;
  private kafkaProducer: KafkaProducer;
  private trainingResultConsumer: KafkaConsumer;
  private trainingResultHandler!: TrainingResultHandler;
  private allRouters: Router[] = [];
  private routeDependencies!: RouteDependencies;

  constructor() {
    this.app = express();
    this.db = new DatabaseConnection(); //TODO: need to pass env variable for this one
    this.initializeMiddleware();
    const kafkaBrokers = config.kafka.brokers;
    this.kafkaProducer = new KafkaProducer({
      clientId: "bot-builder-service-producer",
      brokers: kafkaBrokers,
    });
    this.trainingResultConsumer = new KafkaConsumer({
      groupId: "bot-builder-result-group",
      brokers: kafkaBrokers,
    });
  }

  private initializeServices(): void {
    this.flowService = new FlowService(this.db.models);
    const rasaService = new RasaService(this.flowService);
    this.botService = new BotService(this.db.models, rasaService, this.db);
    this.botController = new BotController(this.botService, this.db.models);
    this.flowController = new FlowController(this.flowService);
    this.appController = new AppController();
    this.buildService = new BuildService(this.db, this.kafkaProducer);
    this.buildController = new BuildController(this.buildService);

    this.trainingResultHandler = new TrainingResultHandler(this.buildService);
    // this.trainingResultConsumer = new TrainingResultConsumer(this.buildService);

    const routeDependencies = {
      appController: this.appController,
      buildController: this.buildController,
      models: this.db.models,
    };

    this.allRouters = createRoutes(routeDependencies);
  }
  private async initializeKafka(): Promise<void> {
    await this.kafkaProducer.connect();
    await this.trainingResultConsumer.connect();
    await this.trainingResultConsumer.subscribeAndRun(
      "training-results",
      this.trainingResultHandler.handleMessage,
    );
  }

  private initializeMiddleware(): void {
    // Security middleware
    this.app.use(helmet());

    // CORS configuration
    // this.app.use(
    //   cors({
    //     origin: config.server.corsOrigins,
    //     credentials: true,
    //     methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    //     allowedHeaders: ["Content-Type", "Authorization"],
    //   })
    // );
    this.app.use(cors());

    // Compression
    this.app.use(compression());

    // Request parsing
    this.app.use(express.json({ limit: "10mb" }));
    this.app.use(express.urlencoded({ extended: true, limit: "10mb" }));

    // Logging
    if (config.server.env !== "test") {
      this.app.use(
        morgan("combined", {
          stream: {
            write: (message: string) => {
              logger.info(message.trim());
            },
          },
        }),
      );
    }

    // Request ID middleware
    this.app.use((req: Request, res: Response, next: NextFunction) => {
      const requestId =
        (req.headers["x-request-id"] as string) ||
        `req_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;

      (req as any).requestId = requestId;
      res.setHeader("X-Request-ID", requestId);
      next();
    });
  }

  private initializeRoutes(): void {
    // Swagger documentation
    this.app.use(
      "/api-docs",
      ...swaggerUi.serve,
      swaggerUi.setup(swaggerSpec, {
        explorer: true,
        swaggerOptions: {
          persistAuthorization: true,
          docExpansion: "list",
        },
      }),
    );

    // Swagger JSON endpoint
    this.app.get("/api-docs.json", (req: Request, res: Response) => {
      res.setHeader("Content-Type", "application/json");
      res.send(swaggerSpec);
    });

    // Health check
    this.app.get("/health", (req: Request, res: Response) => {
      res.json({
        success: true,
        data: {
          status: "healthy",
          service: "bot-builder-service",
          version: process.env.npm_package_version || "1.0.0",
          timestamp: new Date(),
        },
        timestamp: new Date(),
      } as ApiResponse);
    });

    // Bot routes
    this.app.post("/api/v1/bots", (req, res) => this.botController.createBot(req, res));
    this.app.get("/api/v1/bots/:id", (req, res) => this.botController.getBotById(req, res));
    this.app.put("/api/v1/bots/:id", (req, res) => this.botController.updateBot(req, res));
    this.app.delete("/api/v1/bots/:id", (req, res) => this.botController.deleteBot(req, res));
    this.app.get("/api/v1/bots", validateQuery(PaginationQuerySchema), (req, res) =>
      this.botController.getBots(req, res),
    );
    this.app.post("/api/v1/bots/:id/activate", (req, res) =>
      this.botController.activateBot(req, res),
    );
    this.app.post("/api/v1/bots/:id/deactivate", (req, res) =>
      this.botController.deactivateBot(req, res),
    );
    this.app.post("/api/v1/bots/:id/build", (req, res) =>
      this.buildController.createBuild(req, res),
    );

    // Channel routes
    this.app.get("/api/v1/bots/:botId/channels/:channelType", (req, res) =>
      this.botController.getChannelConfig(req, res),
    );
    this.app.post("/api/v1/bots/:botId/channels", (req, res) =>
      this.botController.createChannelIntegration(req, res),
    );
    this.app.put("/api/v1/bots/:botId/channels/:channelId", (req, res) =>
      this.botController.updateChannelIntegration(req, res),
    );

    // Flow routes
    this.app.post("/api/v1/flows", (req, res) => this.flowController.createFlow(req, res));
    this.app.get("/api/v1/flows/:id", (req, res) => this.flowController.getFlowById(req, res));
    this.app.put("/api/v1/flows/:id", (req, res) => this.flowController.updateFlow(req, res));
    this.app.delete("/api/v1/flows/:id/apps/:appId", (req, res) =>
      this.flowController.deleteFlow(req, res),
    );
    this.app.get("/api/v1/bots/:botId/flows", (req, res) =>
      this.flowController.getFlowsByBot(req, res),
    );
    this.app.post("/api/v1/bots/:botId/flows/bulk", (req, res) =>
      this.flowController.bulkCreateFlows(req, res),
    );

    this.app.use("/api/v1", ...this.allRouters);

    // Root endpoint
    this.app.get("/", (req: Request, res: Response) => {
      res.json({
        success: true,
        data: {
          service: "bot-builder-service",
          version: process.env.npm_package_version || "1.0.0",
          environment: config.server.env,
          timestamp: new Date(),
        },
        timestamp: new Date(),
      } as ApiResponse);
    });
  }

  private initializeErrorHandling(): void {
    // 404 handler (must be last route)
    this.app.use("*", (req: Request, res: Response) => {
      res.status(404).json({
        success: false,
        error: {
          code: "NOT_FOUND",
          message: `Route ${req.method} ${req.originalUrl} not found`,
        },
        timestamp: new Date(),
      } as ApiResponse);
    });

    // Global error handler
    this.app.use((error: Error, req: Request, res: Response, next: NextFunction) => {
      logger.error("Unhandled error:", {
        error: error.message,
        stack: error.stack,
        requestId: (req as any).requestId,
        method: req.method,
        path: req.path,
      });

      const message =
        config.server.env === "production" ? "An internal error occurred" : error.message;

      res.status(500).json({
        success: false,
        error: {
          code: "INTERNAL_ERROR",
          message,
          ...(config.server.env !== "production" && { stack: error.stack }),
        },
        timestamp: new Date(),
      } as ApiResponse);
    });
  }

  public async start(): Promise<void> {
    try {
      // Connect to database and initialize models
      await this.db.connect();
      // Initialize services, controllers, and routes
      this.initializeServices();
      this.initializeRoutes();
      this.initializeErrorHandling();

      this.initializeKafka();

      // Start server
      const port = config.server.port;
      this.app.listen(port, () => {
        logger.info(`Bot Builder Service started on port ${port}`);
        logger.info(`Environment: ${config.server.env}`);
        logger.info(`Health check: http://localhost:${port}/health`);
      });
    } catch (error) {
      logger.error("Failed to start application:", error);
      process.exit(1);
    }
  }

  public async stop(): Promise<void> {
    try {
      logger.info("Shutting down Bot Builder Service...");
      await this.trainingResultConsumer.disconnect();
      await this.kafkaProducer.disconnect();
      await this.db.disconnect();
      logger.info("Bot Builder Service stopped");
    } catch (error) {
      logger.error("Error during shutdown:", error);
    }
  }
}
