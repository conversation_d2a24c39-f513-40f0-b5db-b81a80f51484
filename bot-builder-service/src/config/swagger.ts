/**
 * Swagger Configuration for Bot Builder Service
 *
 * OpenAPI 3.0 specification for all administrative APIs
 */

import swaggerJsdoc from "swagger-jsdoc";
import { SwaggerDefinition } from "swagger-jsdoc";

const swaggerDefinition: SwaggerDefinition = {
  openapi: "3.0.0",
  info: {
    title: "Bot Builder Service API",
    version: "1.0.0",
    description:
      "Administrative API for managing bots, flows, and configurations in the no-code chatbot platform",
    contact: {
      name: "Chatbot Platform Team",
      email: "<EMAIL>",
    },
    license: {
      name: "MIT",
      url: "https://opensource.org/licenses/MIT",
    },
  },
  servers: [
    {
      url: "http://localhost:3000",
      description: "Development server",
    },
    {
      url: "https://api.chatbot-platform.com",
      description: "Production server",
    },
  ],
  components: {
    securitySchemes: {
      ApiKeyAuth: {
        type: "apiKey",
        in: "header",
        name: "X-API-Key",
        description: "API key for authentication",
      },
      BearerAuth: {
        type: "http",
        scheme: "bearer",
        bearerFormat: "JWT",
        description: "JWT token for user authentication",
      },
    },
    schemas: {
      Bot: {
        type: "object",
        required: ["id", "name", "status"],
        properties: {
          id: {
            type: "string",
            format: "uuid",
            description: "Unique bot identifier",
            example: "123e4567-e89b-12d3-a456-************",
          },
          name: {
            type: "string",
            description: "Bot name",
            example: "Customer Support Bot",
            minLength: 1,
            maxLength: 255,
          },
          description: {
            type: "string",
            description: "Bot description",
            example: "Handles customer inquiries and support requests",
            maxLength: 1000,
          },
          status: {
            type: "string",
            enum: ["active", "inactive", "draft"],
            description: "Bot status",
            example: "active",
          },
          settings: {
            type: "object",
            description: "Bot configuration settings",
            properties: {
              nlu: {
                type: "object",
                properties: {
                  provider: { type: "string", example: "rasa" },
                  confidenceThreshold: { type: "number", example: 0.7 },
                },
              },
              session: {
                type: "object",
                properties: {
                  ttlMinutes: { type: "number", example: 30 },
                  maxConcurrentSessions: { type: "number", example: 10 },
                },
              },
            },
          },
          metadata: {
            type: "object",
            description: "Additional metadata",
            additionalProperties: true,
          },
          createdAt: {
            type: "string",
            format: "date-time",
            description: "Creation timestamp",
          },
          updatedAt: {
            type: "string",
            format: "date-time",
            description: "Last update timestamp",
          },
          createdBy: {
            type: "string",
            description: "User who created the bot",
          },
          updatedBy: {
            type: "string",
            description: "User who last updated the bot",
          },
        },
      },
      Flow: {
        type: "object",
        required: ["id", "name", "botId", "entryNodeId", "nodes"],
        properties: {
          id: {
            type: "string",
            format: "uuid",
            description: "Unique flow identifier",
          },
          name: {
            type: "string",
            description: "Flow name",
            example: "Greeting Flow",
          },
          description: {
            type: "string",
            description: "Flow description",
          },
          botId: {
            type: "string",
            format: "uuid",
            description: "Associated bot ID",
          },
          version: {
            type: "integer",
            description: "Flow version number",
            example: 1,
          },
          isActive: {
            type: "boolean",
            description: "Whether the flow is active",
            example: true,
          },
          entryNodeId: {
            type: "string",
            description: "ID of the entry node",
            example: "intent_greet",
          },
          nodes: {
            type: "object",
            description: "Flow nodes definition",
            additionalProperties: {
              $ref: "#/components/schemas/Node",
            },
          },
          createdAt: {
            type: "string",
            format: "date-time",
          },
          updatedAt: {
            type: "string",
            format: "date-time",
          },
        },
      },
      Node: {
        type: "object",
        required: ["id", "type", "condition"],
        properties: {
          id: {
            type: "string",
            description: "Node identifier",
          },
          type: {
            type: "string",
            enum: [
              "intent",
              "message",
              "form",
              "request",
              "script",
              "flow_connector",
            ],
            description: "Node type",
          },
          condition: {
            type: "array",
            items: {
              $ref: "#/components/schemas/Condition",
            },
            description: "Conditions for routing to next node",
          },
        },
        discriminator: {
          propertyName: "type",
        },
      },
      Condition: {
        type: "object",
        required: ["than"],
        properties: {
          variable: {
            type: "string",
            description: "Context variable path",
            example: "context.user.age",
          },
          value: {
            description: "Expected value for comparison",
            example: 18,
          },
          type: {
            type: "string",
            enum: [
              "equal",
              "not_equal",
              "is_defined",
              "is_undefined",
              "greater_than",
              "less_than",
              "contains",
              "regex_match",
            ],
            description: "Comparison type",
            example: "greater_than",
          },
          than: {
            type: "string",
            description: "Next node ID",
            example: "message_welcome",
          },
        },
      },
      CreateBotRequest: {
        type: "object",
        required: ["name"],
        properties: {
          name: {
            type: "string",
            description: "Bot name",
            example: "Customer Support Bot",
          },
          description: {
            type: "string",
            description: "Bot description",
          },
          settings: {
            type: "object",
            description: "Bot settings",
          },
          metadata: {
            type: "object",
            description: "Additional metadata",
          },
        },
      },
      UpdateBotRequest: {
        type: "object",
        properties: {
          name: {
            type: "string",
            description: "Bot name",
          },
          description: {
            type: "string",
            description: "Bot description",
          },
          settings: {
            type: "object",
            description: "Bot settings",
          },
          metadata: {
            type: "object",
            description: "Additional metadata",
          },
        },
      },
      CreateFlowRequest: {
        type: "object",
        required: ["name", "botId", "entryNodeId", "nodes"],
        properties: {
          name: {
            type: "string",
            description: "Flow name",
          },
          description: {
            type: "string",
            description: "Flow description",
          },
          botId: {
            type: "string",
            format: "uuid",
            description: "Associated bot ID",
          },
          entryNodeId: {
            type: "string",
            description: "Entry node ID",
          },
          nodes: {
            type: "object",
            description: "Flow nodes",
          },
          metadata: {
            type: "object",
            description: "Additional metadata",
          },
        },
      },
      ApiResponse: {
        type: "object",
        required: ["success", "timestamp"],
        properties: {
          success: {
            type: "boolean",
            description: "Whether the request was successful",
          },
          data: {
            description: "Response data (present on success)",
          },
          error: {
            type: "object",
            description: "Error details (present on failure)",
            properties: {
              code: {
                type: "string",
                description: "Error code",
              },
              message: {
                type: "string",
                description: "Error message",
              },
              details: {
                description: "Additional error details",
              },
            },
          },
          timestamp: {
            type: "string",
            format: "date-time",
            description: "Response timestamp",
          },
        },
      },
      PaginationResponse: {
        type: "object",
        properties: {
          items: {
            type: "array",
            description: "Array of items",
          },
          pagination: {
            type: "object",
            properties: {
              page: { type: "integer", example: 1 },
              limit: { type: "integer", example: 20 },
              total: { type: "integer", example: 100 },
              totalPages: { type: "integer", example: 5 },
              hasNext: { type: "boolean", example: true },
              hasPrev: { type: "boolean", example: false },
            },
          },
        },
      },
      Pagination: {
        type: "object",
        properties: {
          page: { type: "integer", example: 1 },
          limit: { type: "integer", example: 20 },
          total: { type: "integer", example: 100 },
          totalPages: { type: "integer", example: 5 },
          hasNext: { type: "boolean", example: true },
          hasPrev: { type: "boolean", example: false },
        },
      },
      KnowledgeUnit: {
        type: "object",
        required: ["botId", "name", "type"],
        properties: {
          id: { type: "string", format: "uuid" },
          botId: { type: "string", format: "uuid" },
          name: { type: "string" },
          type: { type: "string", enum: ["faq", "intent", "small_talk"] },
          metadata: { type: "object" },
          flowId: { type: "string", format: "uuid", nullable: true },
          createdAt: { type: "string", format: "date-time" },
          updatedAt: { type: "string", format: "date-time" },
          deletedAt: { type: "string", format: "date-time", nullable: true },
          createdBy: { type: "string", format: "uuid" },
          updatedBy: { type: "string", format: "uuid" },
          deletedBy: { type: "string", format: "uuid", nullable: true }
        }
      },
      FaqItems: {
        type: "object",
        properties: {
          id: { type: "string", format: "uuid" },
          knowledgeUnitId: { type: "string", format: "uuid" },
          questions: { type: "array", items: { type: "string" } },
          answer: { type: "string" },
          createdAt: { type: "string", format: "date-time" },
          updatedAt: { type: "string", format: "date-time" },
          deletedAt: { type: "string", format: "date-time", nullable: true },
          createdBy: { type: "string", format: "uuid" },
          updatedBy: { type: "string", format: "uuid" },
          deletedBy: { type: "string", format: "uuid", nullable: true }
        }
      },
      IntentItems: {
        type: "object",
        properties: {
          id: { type: "string", format: "uuid" },
          knowledgeUnitId: { type: "string", format: "uuid" },
          text: { type: "string" },
          entities: { type: "array", items: { type: "object" } },
          createdAt: { type: "string", format: "date-time" },
          updatedAt: { type: "string", format: "date-time" },
          deletedAt: { type: "string", format: "date-time", nullable: true },
          createdBy: { type: "string", format: "uuid" },
          updatedBy: { type: "string", format: "uuid" },
          deletedBy: { type: "string", format: "uuid", nullable: true }
        }
      },
      Entities: {
        type: "object",
        properties: {
          id: { type: "string", format: "uuid" },
          name: { type: "string" },
          knowledgeUnitId: { type: "string", format: "uuid", nullable: true },
          createdAt: { type: "string", format: "date-time" },
          updatedAt: { type: "string", format: "date-time" },
          deletedAt: { type: "string", format: "date-time", nullable: true },
          createdBy: { type: "string", format: "uuid" },
          updatedBy: { type: "string", format: "uuid" },
          deletedBy: { type: "string", format: "uuid", nullable: true }
        }
      }
    },
  },
  tags: [
    {
      name: "Bots",
      description: "Bot management operations",
    },
    {
      name: "Flows",
      description: "Flow management operations",
    },
    {
      name: "Health",
      description: "Health check endpoints",
    },
    {
      name: "Knowledge Units",
      description: "Knowledge unit management operations",
    },
    {
      name: "FAQ Items",
      description: "FAQ item management operations",
    },
    {
      name: "Intent Items",
      description: "Intent item management operations",
    },
    {
      name: "Entities",
      description: "Entity management operations",
    },
  ],
};

const options = {
  definition: swaggerDefinition,
  apis: [
    "./src/controllers/*.ts",
    "./src/routes/*.ts",
    "../routes/*.ts",
    "../controllers/*.ts",
    "./routes/*.ts",
    "./controllers/*.ts",
  ],
  swaggerOptions: {
    persistAuthorization: true
  }
};

export const swaggerSpec = swaggerJsdoc(options);
export default swaggerSpec;
