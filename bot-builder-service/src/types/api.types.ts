/**
 * API Request and Response Types
 *
 * These types define the structure of API requests and responses across all services.
 */

import { PaginatedResponse, PaginationParams } from "@neuratalk/common";
import { Bot, BotSettings, CreateFlowRequest } from "./bot.types";
import { FlowAttributes as Flow } from "@neuratalk/bot-store";

type Node = any;
// --- Bot Builder Service APIs ---

export interface CreateBotRequest {
  name: string;
  description?: string;
  domain?: string;
  settings?: BotSettings;
  metadata?: Record<string, any>;
}

export interface CreateBotResponse {
  bot: Bot;
}

export interface UpdateBotRequest {
  name?: string;
  description?: string;
  domain?: string;
  settings?: Partial<BotSettings>;
  metadata?: Record<string, any>;
}

export interface GetBotsRequest extends PaginationParams {
  search?: string;
  status?: "active" | "inactive";
}

export interface GetBotsResponse extends PaginatedResponse<Bot> {}

export interface CreateFlowResponse {
  flow: Flow;
}

export interface UpdateFlowRequest {
  name?: string;
  description?: string;
  nodes?: Record<string, Node>;
  entryNodeId?: string;
  isActive?: boolean;
  metadata?: Record<string, any>;
}

export interface GetFlowsRequest extends PaginationParams {
  botId: string;
  search?: string;
  isActive?: boolean;
}

export interface GetFlowsResponse extends PaginatedResponse<Flow> {}

export interface BulkCreateFlowsRequest {
  botId: string;
  flows: CreateFlowRequest[];
}

export interface BulkCreateFlowsResponse {
  created: Flow[];
  failed: Array<{
    request: CreateFlowRequest;
    error: string;
  }>;
}
