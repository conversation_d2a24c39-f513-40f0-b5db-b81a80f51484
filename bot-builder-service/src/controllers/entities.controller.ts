import { Request, Response } from "express";
import { Models } from "@neuratalk/bot-store";
import { ApiResponse, getPaginatedResults, PaginationQuery, UuidParams } from "@neuratalk/common";
import { CreateEntityRequest, UpdateEntityRequest } from "../schemas";
import { logger } from "@neuratalk/common";
import { WhereOptions } from "sequelize";

export class EntitiesController {
  private models: Models;

  constructor(models: Models) {
    this.models = models;
  }

  /**
   * @swagger
   * /api/v1/entities:
   *   post:
   *     summary: Create entity
   *     tags: [Entities]
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - name
   *             properties:
   *               name:
   *                 type: string
   *                 minLength: 1
   *                 maxLength: 100
   *               knowledgeUnitId:
   *                 type: string
   *                 format: uuid
   *                 description: ID of the knowledge unit this entity belongs to
   *                 nullable: true
   *     responses:
   *       201:
   *         description: Created
   */
  async create(req: Request<any, any, CreateEntityRequest>, res: Response): Promise<void> {
    try {
      const userId = req.user?.id || "system"; //TODO: need to remove system fallback
      const entity = await this.models.Entities.create({
        ...req.body,
        createdBy: userId,
        updatedBy: userId,
      });

      logger.info(`Entity created: ${entity.id}`);
      res.status(201).json({
        success: true,
        data: entity,
        timestamp: new Date(),
      } as ApiResponse);
    } catch (error: any) {
      logger.error("Error creating entity:", error);
      res.status(400).json({
        success: false,
        error: {
          code: "VALIDATION_ERROR",
          message: error.message,
        },
        timestamp: new Date(),
      } as ApiResponse);
    }
  }

  /**
   * @swagger
   * /api/v1/entities:
   *   get:
   *     summary: List entities with filtering and pagination
   *     tags: [Entities]
   *     parameters:
   *       - in: query
   *         name: page
   *         schema:
   *           type: integer
   *           minimum: 1
   *           default: 1
   *       - in: query
   *         name: limit
   *         schema:
   *           type: integer
   *           minimum: 1
   *           maximum: 100
   *           default: 20
   *       - in: query
   *         name: search
   *         schema:
   *           type: string
   *       - in: query
   *         name: filter
   *         schema:
   *           type: string
   *         example: '{"name":{"like":"city"}}'
   *     responses:
   *       200:
   *         description: Success
   */
  async getAll(req: Request<any, any, any, PaginationQuery>, res: Response): Promise<void> {
    try {
      const result = await getPaginatedResults(this.models.Entities, req.query, ["name"]);

      res.json({
        success: true,
        data: result,
        timestamp: new Date(),
      } as ApiResponse);
    } catch (error: any) {
      logger.error("Error fetching entities:", error);
      res.status(500).json({
        success: false,
        error: {
          code: "INTERNAL_ERROR",
          message: "Failed to fetch entities",
        },
        timestamp: new Date(),
      } as ApiResponse);
    }
  }

  /**
   * @swagger
   * /api/v1/entities/{id}:
   *   get:
   *     summary: Get entity by ID
   *     tags: [Entities]
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *     responses:
   *       200:
   *         description: Success
   */
  async getById(req: Request<UuidParams>, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const entity = await this.models.Entities.findOne({
        where: { id, deletedAt: null } as WhereOptions,
      });

      if (!entity) {
        res.status(404).json({
          success: false,
          error: {
            code: "NOT_FOUND",
            message: "Entity not found",
          },
          timestamp: new Date(),
        } as ApiResponse);
        return;
      }

      res.json({
        success: true,
        data: entity,
        timestamp: new Date(),
      } as ApiResponse);
    } catch (error: any) {
      logger.error(`Error fetching entity ${req.params.id}:`, error);
      res.status(500).json({
        success: false,
        error: {
          code: "INTERNAL_ERROR",
          message: "Failed to fetch entity",
        },
        timestamp: new Date(),
      } as ApiResponse);
    }
  }

  /**
   * @swagger
   * /api/v1/entities/{id}:
   *   put:
   *     summary: Update entity
   *     tags: [Entities]
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *     responses:
   *       200:
   *         description: Updated
   */
  async update(req: Request<UuidParams, any, UpdateEntityRequest>, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const userId = req.user?.id || "system";

      const [updated] = await this.models.Entities.update(
        {
          ...req.body,
          updatedBy: userId,
        },
        {
          where: { id, deletedAt: null } as WhereOptions,
        },
      );

      if (!updated) {
        res.status(404).json({
          success: false,
          error: {
            code: "NOT_FOUND",
            message: "Entity not found",
          },
          timestamp: new Date(),
        } as ApiResponse);
        return;
      }

      const entity = await this.models.Entities.findByPk(id);
      logger.info(`Entity updated: ${id}`);

      res.json({
        success: true,
        data: entity,
        timestamp: new Date(),
      } as ApiResponse);
    } catch (error: any) {
      logger.error(`Error updating entity ${req.params.id}:`, error);
      res.status(400).json({
        success: false,
        error: {
          code: "VALIDATION_ERROR",
          message: error.message,
        },
        timestamp: new Date(),
      } as ApiResponse);
    }
  }

  /**
   * @swagger
   * /api/v1/entities/{id}:
   *   delete:
   *     summary: Delete entity
   *     tags: [Entities]
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *     responses:
   *       204:
   *         description: Deleted
   */
  async delete(req: Request<UuidParams>, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const deleted = await this.models.Entities.destroy({ where: { id } });

      if (!deleted) {
        res.status(404).json({
          success: false,
          error: {
            code: "NOT_FOUND",
            message: "Entity not found",
          },
          timestamp: new Date(),
        } as ApiResponse);
        return;
      }

      logger.info(`Entity deleted: ${id}`);
      res.status(204).send();
    } catch (error: any) {
      logger.error(`Error deleting entity ${req.params.id}:`, error);
      res.status(500).json({
        success: false,
        error: {
          code: "INTERNAL_ERROR",
          message: "Failed to delete entity",
        },
        timestamp: new Date(),
      } as ApiResponse);
    }
  }
}
