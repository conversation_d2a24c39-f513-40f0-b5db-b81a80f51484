import { Request, Response } from "express";
import { Models } from "@neuratalk/bot-store";
import { ApiResponse, getPaginatedResults, PaginationQuery, UuidParams } from "@neuratalk/common";
import { CreateKnowledgeUnitRequest, UpdateKnowledgeUnitRequest } from "../schemas";
import { logger } from "@neuratalk/common";
import { WhereOptions } from "sequelize";

export class KnowledgeUnitController {
  private models: Models;

  constructor(models: Models) {
    this.models = models;
  }

  /**
   * @swagger
   * /api/v1/knowledge-units:
   *   get:
   *     summary: List knowledge units with filtering and pagination
   *     tags: [Knowledge Units]
   *     parameters:
   *       - in: query
   *         name: page
   *         schema:
   *           type: integer
   *           minimum: 1
   *           default: 1
   *         description: Page number
   *       - in: query
   *         name: limit
   *         schema:
   *           type: integer
   *           minimum: 1
   *           maximum: 100
   *           default: 20
   *         description: Items per page
   *       - in: query
   *         name: search
   *         schema:
   *           type: string
   *         description: Search term for name field
   *       - in: query
   *         name: sortBy
   *         schema:
   *           type: string
   *           default: createdAt
   *         description: Field to sort by
   *       - in: query
   *         name: sortOrder
   *         schema:
   *           type: string
   *           enum: [asc, desc]
   *           default: desc
   *         description: Sort order
   *       - in: query
   *         name: filter
   *         schema:
   *           type: string
   *         description: JSON string with filter conditions (e.g., {"type":{"eq":"faq"}})
   *         example: '{"type":{"eq":"faq"}}'
   *     responses:
   *       200:
   *         description: Success
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 success:
   *                   type: boolean
   *                 data:
   *                   type: object
   *                   properties:
   *                     items:
   *                       type: array
   *                       items:
   *                         $ref: '#/components/schemas/KnowledgeUnitModel'
   *                     pagination:
   *                       $ref: '#/components/schemas/Pagination'
   *                 timestamp:
   *                   type: string
   *                   format: date-time
   */

  /**
   * @swagger
   * /api/v1/knowledge-units:
   *   post:
   *     summary: Create a new knowledge unit
   *     tags: [Knowledge Units]
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - botId
   *               - name
   *               - type
   *             properties:
   *               botId:
   *                 type: string
   *                 format: uuid
   *                 description: ID of the bot this knowledge unit belongs to
   *                 example: 123e4567-e89b-12d3-a456-************
   *               name:
   *                 type: string
   *                 minLength: 1
   *                 maxLength: 255
   *                 example: Customer Support FAQ
   *               type:
   *                 type: string
   *                 enum: [faq, intent, small_talk]
   *                 example: faq
   *               metadata:
   *                 type: object
   *                 additionalProperties: true
   *                 example: {"category": "support"}
   *               flowId:
   *                 type: string
   *                 format: uuid
   *                 description: ID of the flow this knowledge unit belongs to (optional) (Foreign Key to flows table)
   *                 nullable: true
   *     responses:
   *       201:
   *         description: Knowledge unit created successfully
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 success:
   *                   type: boolean
   *                 data:
   *                   $ref: '#/components/schemas/KnowledgeUnit'
   *                 timestamp:
   *                   type: string
   *                   format: date-time
   */
  async create(req: Request<any, any, CreateKnowledgeUnitRequest>, res: Response): Promise<void> {
    try {
      const userId = req.user?.id || "system";
      const knowledgeUnit = await this.models.KnowledgeUnit.create({
        ...req.body,
        createdBy: userId,
        updatedBy: userId,
      } as any);

      logger.info(`Knowledge unit created: ${knowledgeUnit.id}`);
      res.status(201).json({
        success: true,
        data: knowledgeUnit,
        timestamp: new Date(),
      } as ApiResponse);
    } catch (error: any) {
      logger.error("Error creating knowledge unit:", error);
      res.status(400).json({
        success: false,
        error: {
          code: "VALIDATION_ERROR",
          message: error.message,
        },
        timestamp: new Date(),
      } as ApiResponse);
    }
  }

  /**
   * @swagger
   * /api/v1/knowledge-units:
   *   get:
   *     summary: List knowledge units with filtering and pagination
   *     tags: [Knowledge Units]
   *     parameters:
   *       - in: query
   *         name: page
   *         schema:
   *           type: integer
   *           minimum: 1
   *           default: 1
   *       - in: query
   *         name: limit
   *         schema:
   *           type: integer
   *           minimum: 1
   *           maximum: 100
   *           default: 20
   *       - in: query
   *         name: search
   *         schema:
   *           type: string
   *       - in: query
   *         name: filter
   *         schema:
   *           type: string
   *         example: '{"type":{"eq":"faq"}}'
   *     responses:
   *       200:
   *         description: Success
   */
  getAll = async (req: Request<any, any, any, PaginationQuery>, res: Response) => {
    try {
      const result = await getPaginatedResults(this.models.KnowledgeUnit, req.query, ["name"]);

      res.json({
        success: true,
        data: result,
        timestamp: new Date(),
      } as ApiResponse);
    } catch (error: any) {
      logger.error("Error fetching knowledge units:", error);
      res.status(500).json({
        success: false,
        error: {
          code: "INTERNAL_ERROR",
          message: "Failed to fetch knowledge units",
        },
        timestamp: new Date(),
      } as ApiResponse);
    }
  };

  /**
   * @swagger
   * /api/v1/knowledge-units/{id}:
   *   get:
   *     summary: Get knowledge unit by ID
   *     tags: [Knowledge Units]
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *     responses:
   *       200:
   *         description: Success
   */
  async getById(req: Request<UuidParams>, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const knowledgeUnit = await this.models.KnowledgeUnit.findOne({
        where: { id, deletedAt: null } as WhereOptions,
      });

      if (!knowledgeUnit) {
        res.status(404).json({
          success: false,
          error: {
            code: "NOT_FOUND",
            message: "Knowledge unit not found",
          },
          timestamp: new Date(),
        } as ApiResponse);
        return;
      }

      res.json({
        success: true,
        data: knowledgeUnit,
        timestamp: new Date(),
      } as ApiResponse);
    } catch (error: any) {
      logger.error(`Error fetching knowledge unit ${req.params.id}:`, error);
      res.status(500).json({
        success: false,
        error: {
          code: "INTERNAL_ERROR",
          message: "Failed to fetch knowledge unit",
        },
        timestamp: new Date(),
      } as ApiResponse);
    }
  }

  /**
   * @swagger
   * /api/v1/knowledge-units/{id}:
   *   put:
   *     summary: Update knowledge unit
   *     tags: [Knowledge Units]
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *     requestBody:
   *       required: false
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             properties:
   *               botId:
   *                 type: string
   *                 format: uuid
   *                 description: ID of the bot this knowledge unit belongs to
   *                 example: 123e4567-e89b-12d3-a456-************
   *               name:
   *                 type: string
   *                 minLength: 1
   *                 maxLength: 255
   *                 example: Customer Support FAQ
   *               type:
   *                 type: string
   *                 enum: [faq, intent, small_talk]
   *                 example: faq
   *               metadata:
   *                 type: object
   *                 additionalProperties: true
   *                 example: {"category": "support"}
   *               flowId:
   *                 type: string
   *                 format: uuid
   *                 description: ID of the flow this knowledge unit belongs to (optional) (Foreign Key to flows table)
   *                 nullable: true
   */
  async update(
    req: Request<UuidParams, any, UpdateKnowledgeUnitRequest>,
    res: Response,
  ): Promise<void> {
    try {
      const { id } = req.params;
      const userId = req.user?.id || "system";

      const [updated] = await this.models.KnowledgeUnit.update(
        {
          ...req.body,
          updatedBy: userId,
        } as any,
        {
          where: { id, deletedAt: null } as WhereOptions,
        },
      );

      if (!updated) {
        res.status(404).json({
          success: false,
          error: {
            code: "NOT_FOUND",
            message: "Knowledge unit not found",
          },
          timestamp: new Date(),
        } as ApiResponse);
        return;
      }

      const knowledgeUnit = await this.models.KnowledgeUnit.findByPk(id);
      logger.info(`Knowledge unit updated: ${id}`);

      res.json({
        success: true,
        data: knowledgeUnit,
        timestamp: new Date(),
      } as ApiResponse);
    } catch (error: any) {
      logger.error(`Error updating knowledge unit ${req.params.id}:`, error);
      res.status(400).json({
        success: false,
        error: {
          code: "VALIDATION_ERROR",
          message: error.message,
        },
        timestamp: new Date(),
      } as ApiResponse);
    }
  }

  /**
   * @swagger
   * /api/v1/knowledge-units/{id}:
   *   delete:
   *     summary: Delete knowledge unit
   *     tags: [Knowledge Units]
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *     responses:
   *       204:
   *         description: Deleted
   */
  async delete(req: Request<UuidParams>, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const deleted = await this.models.KnowledgeUnit.destroy({
        where: { id },
      });

      if (!deleted) {
        res.status(404).json({
          success: false,
          error: {
            code: "NOT_FOUND",
            message: "Knowledge unit not found",
          },
          timestamp: new Date(),
        } as ApiResponse);
        return;
      }

      logger.info(`Knowledge unit deleted: ${id}`);
      res.status(204).send();
    } catch (error: any) {
      logger.error(`Error deleting knowledge unit ${req.params.id}:`, error);
      res.status(500).json({
        success: false,
        error: {
          code: "INTERNAL_ERROR",
          message: "Failed to delete knowledge unit",
        },
        timestamp: new Date(),
      } as ApiResponse);
    }
  }
}
