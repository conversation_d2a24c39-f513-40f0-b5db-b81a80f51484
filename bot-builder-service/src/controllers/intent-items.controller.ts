import { Request, Response } from "express";
import { Models } from "@neuratalk/bot-store";
import { ApiResponse, getPaginatedResults, PaginationQuery, UuidParams } from "@neuratalk/common";
import { CreateIntentItemRequest, UpdateIntentItemRequest } from "../schemas";
import { logger } from "@neuratalk/common";
import { WhereOptions } from "sequelize";

export class IntentItemsController {
  private models: Models;

  constructor(models: Models) {
    this.models = models;
  }

  /**
   * @swagger
   * /api/v1/intent-items:
   *   post:
   *     summary: Create intent item
   *     tags: [Intent Items]
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - knowledgeUnitId
   *               - text
   *             properties:
   *               knowledgeUnitId:
   *                 type: string
   *                 format: uuid
   *               text:
   *                 type: string
   *               entities:
   *                 type: array
   *                 items:
   *                   type: object
   *                   properties:
   *                     id:
   *                       type: string
   *                       format: uuid
   *                     name:
   *                       type: string
   *                     value:
   *                       type: string
   *     responses:
   *       201:
   *         description: Created
   */
  async create(req: Request<any, any, CreateIntentItemRequest>, res: Response): Promise<void> {
    try {
      const userId = req.user?.id || "system"; //TODO: need to remove this fallback
      const intentItem = await this.models.IntentItems.create({
        ...req.body,
        createdBy: userId,
        updatedBy: userId,
      });

      logger.info(`Intent item created: ${intentItem.id}`);
      res.status(201).json({
        success: true,
        data: intentItem,
        timestamp: new Date(),
      } as ApiResponse);
    } catch (error: any) {
      logger.error("Error creating intent item:", error);
      res.status(400).json({
        success: false,
        error: {
          code: "VALIDATION_ERROR",
          message: error.message,
        },
        timestamp: new Date(),
      } as ApiResponse);
    }
  }

  /**
   * @swagger
   * /api/v1/intent-items:
   *   get:
   *     summary: List intent items with filtering and pagination
   *     tags: [Intent Items]
   *     parameters:
   *       - in: query
   *         name: page
   *         schema:
   *           type: integer
   *           minimum: 1
   *           default: 1
   *       - in: query
   *         name: limit
   *         schema:
   *           type: integer
   *           minimum: 1
   *           maximum: 100
   *           default: 20
   *       - in: query
   *         name: search
   *         schema:
   *           type: string
   *       - in: query
   *         name: filter
   *         schema:
   *           type: string
   *         example: '{"knowledgeUnitId":{"eq":"uuid"}}'
   *     responses:
   *       200:
   *         description: Success
   */
  async getAll(req: Request<any, any, any, PaginationQuery>, res: Response): Promise<void> {
    try {
      const result = await getPaginatedResults(
        this.models.IntentItems,
        req.query,
        ["text"],
      );

      res.json({
        success: true,
        data: result,
        timestamp: new Date(),
      } as ApiResponse);
    } catch (error: any) {
      logger.error("Error fetching intent items:", error);
      res.status(500).json({
        success: false,
        error: {
          code: "INTERNAL_ERROR",
          message: "Failed to fetch intent items",
        },
        timestamp: new Date(),
      } as ApiResponse);
    }
  }

  /**
   * @swagger
   * /api/v1/intent-items/{id}:
   *   get:
   *     summary: Get intent item by ID
   *     tags: [Intent Items]
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *     responses:
   *       200:
   *         description: Success
   */
  async getById(req: Request<UuidParams>, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const intentItem = await this.models.IntentItems.findOne({
        where: { id, deletedAt: null } as WhereOptions,
      });

      if (!intentItem) {
        res.status(404).json({
          success: false,
          error: {
            code: "NOT_FOUND",
            message: "Intent item not found",
          },
          timestamp: new Date(),
        } as ApiResponse);
        return;
      }

      res.json({
        success: true,
        data: intentItem,
        timestamp: new Date(),
      } as ApiResponse);
    } catch (error: any) {
      logger.error(`Error fetching intent item ${req.params.id}:`, error);
      res.status(500).json({
        success: false,
        error: {
          code: "INTERNAL_ERROR",
          message: "Failed to fetch intent item",
        },
        timestamp: new Date(),
      } as ApiResponse);
    }
  }

  /**
   * @swagger
   * /api/v1/intent-items/{id}:
   *   put:
   *     summary: Update intent item
   *     tags: [Intent Items]
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *     responses:
   *       200:
   *         description: Updated
   */
  async update(
    req: Request<UuidParams, any, UpdateIntentItemRequest>,
    res: Response,
  ): Promise<void> {
    try {
      const { id } = req.params;
      const userId = req.user?.id || "system";

      const [updated] = await this.models.IntentItems.update(
        {
          ...req.body,
          updatedBy: userId,
        },
        {
          where: { id, deletedAt: null } as WhereOptions,
        },
      );

      if (!updated) {
        res.status(404).json({
          success: false,
          error: {
            code: "NOT_FOUND",
            message: "Intent item not found",
          },
          timestamp: new Date(),
        } as ApiResponse);
        return;
      }

      const intentItem = await this.models.IntentItems.findByPk(id);
      logger.info(`Intent item updated: ${id}`);

      res.json({
        success: true,
        data: intentItem,
        timestamp: new Date(),
      } as ApiResponse);
    } catch (error: any) {
      logger.error(`Error updating intent item ${req.params.id}:`, error);
      res.status(400).json({
        success: false,
        error: {
          code: "VALIDATION_ERROR",
          message: error.message,
        },
        timestamp: new Date(),
      } as ApiResponse);
    }
  }

  /**
   * @swagger
   * /api/v1/intent-items/{id}:
   *   delete:
   *     summary: Delete intent item
   *     tags: [Intent Items]
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *     responses:
   *       204:
   *         description: Deleted
   */
  async delete(req: Request<UuidParams>, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const deleted = await this.models.IntentItems.destroy({ where: { id } });

      if (!deleted) {
        res.status(404).json({
          success: false,
          error: {
            code: "NOT_FOUND",
            message: "Intent item not found",
          },
          timestamp: new Date(),
        } as ApiResponse);
        return;
      }

      logger.info(`Intent item deleted: ${id}`);
      res.status(204).send();
    } catch (error: any) {
      logger.error(`Error deleting intent item ${req.params.id}:`, error);
      res.status(500).json({
        success: false,
        error: {
          code: "INTERNAL_ERROR",
          message: "Failed to delete intent item",
        },
        timestamp: new Date(),
      } as ApiResponse);
    }
  }
}
