/**
 * Flow Controller
 *
 * Handles HTTP requests for flow management operations.
 *
 * @swagger
 * components:
 *   schemas:
 *     FlowResponse:
 *       allOf:
 *         - $ref: '#/components/schemas/ApiResponse'
 *         - type: object
 *           properties:
 *             data:
 *               $ref: '#/components/schemas/Flow'
 *     FlowsResponse:
 *       allOf:
 *         - $ref: '#/components/schemas/ApiResponse'
 *         - type: object
 *           properties:
 *             data:
 *               allOf:
 *                 - $ref: '#/components/schemas/PaginationResponse'
 *                 - type: object
 *                   properties:
 *                     items:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/Flow'
 */

import { Request, Response } from "express";
import { FlowService } from "../services/flow.service";
import {
  CreateFlowRequest,
  UpdateFlowRequest,
  GetFlowsRequest,
  CreateFlowResponse,
  GetFlowsResponse,
} from "../types";
import { getTotalPages } from "../utils/api";
import { getStudioAppsService } from "api_gw";
import { ApiResponse, logger } from "@neuratalk/common";
import { FlowAttributes as Flow } from "@neuratalk/bot-store";

export class FlowController {
  private flowService: FlowService;
  private studioAppsService;

  constructor(flowService: FlowService) {
    this.flowService = flowService;
    this.studioAppsService = getStudioAppsService();
  }

  /**
   * @swagger
   * /api/v1/flows:
   *   post:
   *     summary: Create a new flow
   *     description: Creates a new flow for a specific bot
   *     tags: [Flows]
   *     security:
   *       - BearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             $ref: '#/components/schemas/CreateFlowRequest'
   *     responses:
   *       201:
   *         description: Flow created successfully
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/FlowResponse'
   *       400:
   *         description: Invalid request data
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ApiResponse'
   *       500:
   *         description: Internal server error
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ApiResponse'
   */
  async createFlow(req: Request, res: Response): Promise<void> {
    try {
      const request: CreateFlowRequest = { ...req.body };
      const botId = (request as any).botId;

      req.body = {
        name: request.name,
        appData: request.appData,
        ngage_id: request.ngage_id,
        svg: request.svg,
        owner: request.owner,
        createdBy: request.createdBy,
        modifiedBy: request.modifiedBy,
      };

      const appInfo = await this.studioAppsService.createAppInfo(req, res);
      if (res.headersSent) {
        return;
      }

      const flow = await this.flowService.createFlow(request, botId, appInfo.id);

      const response: CreateFlowResponse = { flow };

      res.status(201).json({
        success: true,
        data: response,
        timestamp: new Date(),
      } as ApiResponse<CreateFlowResponse>);
    } catch (error) {
      logger.error("Error in createFlow controller:", error);
      res.status(500).json({
        success: false,
        error: {
          code: "INTERNAL_ERROR",
          message: "Failed to create flow",
        },
        timestamp: new Date(),
      } as ApiResponse);
    }
  }

  /**
   * @swagger
   * /api/v1/flows/{id}:
   *   get:
   *     summary: Get flow by ID
   *     description: Retrieves a specific flow by its unique identifier
   *     tags: [Flows]
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *         description: Flow unique identifier
   *     responses:
   *       200:
   *         description: Flow retrieved successfully
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/FlowResponse'
   *       404:
   *         description: Flow not found
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ApiResponse'
   *       500:
   *         description: Internal server error
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ApiResponse'
   */
  async getFlowById(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;

      const flow = await this.flowService.getFlowById(id);

      if (!flow) {
        res.status(404).json({
          success: false,
          error: {
            code: "FLOW_NOT_FOUND",
            message: "Flow not found",
          },
          timestamp: new Date(),
        } as ApiResponse);
        return;
      }

      res.json({
        success: true,
        data: flow,
        timestamp: new Date(),
      } as ApiResponse<Flow>);
    } catch (error) {
      logger.error("Error in getFlowById controller:", error);
      res.status(500).json({
        success: false,
        error: {
          code: "INTERNAL_ERROR",
          message: "Failed to get flow",
        },
        timestamp: new Date(),
      } as ApiResponse);
    }
  }

  /**
   * @swagger
   * /api/v1/flows/{id}:
   *   put:
   *     summary: Update flow
   *     description: Updates an existing flow
   *     tags: [Flows]
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *         description: Flow unique identifier
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             $ref: '#/components/schemas/UpdateFlowRequest'
   *     responses:
   *       200:
   *         description: Flow updated successfully
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/FlowResponse'
   *       404:
   *         description: Flow not found
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ApiResponse'
   *       500:
   *         description: Internal server error
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ApiResponse'
   */
  async updateFlow(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const request: UpdateFlowRequest = req.body;
      const userId = (req as any).user?.id;

      const flow = await this.flowService.updateFlow(id, request, userId);

      if (!flow) {
        res.status(404).json({
          success: false,
          error: {
            code: "FLOW_NOT_FOUND",
            message: "Flow not found",
          },
          timestamp: new Date(),
        } as ApiResponse);
        return;
      }

      res.json({
        success: true,
        data: flow,
        timestamp: new Date(),
      } as ApiResponse<Flow>);
    } catch (error) {
      logger.error("Error in updateFlow controller:", error);
      res.status(500).json({
        success: false,
        error: {
          code: "INTERNAL_ERROR",
          message: "Failed to update flow",
        },
        timestamp: new Date(),
      } as ApiResponse);
    }
  }

  /**
   * @swagger
   * /api/v1/flows/{id}:
   *   delete:
   *     summary: Delete flow
   *     description: Deletes a specific flow
   *     tags: [Flows]
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *         description: Flow unique identifier
   *     responses:
   *       204:
   *         description: Flow deleted successfully
   *       404:
   *         description: Flow not found
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ApiResponse'
   *       500:
   *         description: Internal server error
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ApiResponse'
   */
  async deleteFlow(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;

      const deletedAppInfo = await this.studioAppsService.purgeAppInfo(req, res);
      if (res.headersSent) {
        return;
      }

      const deleted = await this.flowService.deleteFlow(id);

      if (!deleted) {
        res.status(404).json({
          success: false,
          error: {
            code: "FLOW_NOT_FOUND",
            message: "Flow not found",
          },
          timestamp: new Date(),
        } as ApiResponse);
        return;
      }

      res.status(204).send();
    } catch (error) {
      logger.error("Error in deleteFlow controller:", error);
      res.status(500).json({
        success: false,
        error: {
          code: "INTERNAL_ERROR",
          message: "Failed to delete flow",
        },
        timestamp: new Date(),
      } as ApiResponse);
    }
  }

  /**
   * @swagger
   * /api/v1/bots/{botId}/flows:
   *   get:
   *     summary: Get flows by bot ID
   *     description: Retrieves all flows for a specific bot with pagination and filtering
   *     tags: [Flows]
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - in: path
   *         name: botId
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *         description: Bot unique identifier
   *       - in: query
   *         name: page
   *         schema:
   *           type: integer
   *           default: 1
   *         description: Page number
   *       - in: query
   *         name: limit
   *         schema:
   *           type: integer
   *           default: 20
   *         description: Items per page
   *       - in: query
   *         name: search
   *         schema:
   *           type: string
   *         description: Search term for flow name or description
   *       - in: query
   *         name: isActive
   *         schema:
   *           type: boolean
   *         description: Filter by active status
   *     responses:
   *       200:
   *         description: Flows retrieved successfully
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/FlowsResponse'
   *       500:
   *         description: Internal server error
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ApiResponse'
   */
  async getFlowsByBot(req: Request, res: Response): Promise<void> {
    try {
      const { botId } = req.params;
      const { page = 1, limit = 20, search, isActive } = req.query as any;

      const { flows, total } = await this.flowService.getFlowsByBot(
        botId,
        parseInt(page, 10),
        parseInt(limit, 10),
        search,
        isActive === "true" ? true : isActive === "false" ? false : undefined,
      );

      const totalPages = getTotalPages(total, parseInt(limit, 10));

      const response: GetFlowsResponse = {
        items: flows,
        pagination: {
          page: parseInt(page, 10),
          limit: parseInt(limit, 10),
          total,
          totalPages,
          hasNext: parseInt(page, 10) < totalPages,
          hasPrev: parseInt(page, 10) > 1,
        },
      };

      res.json({
        success: true,
        data: response,
        timestamp: new Date(),
      } as ApiResponse<GetFlowsResponse>);
    } catch (error) {
      logger.error("Error in getFlowsByBot controller:", error);
      res.status(500).json({
        success: false,
        error: {
          code: "INTERNAL_ERROR",
          message: "Failed to get flows",
        },
        timestamp: new Date(),
      } as ApiResponse);
    }
  }

  /**
   * @swagger
   * /api/v1/bots/{botId}/flows/bulk:
   *   post:
   *     summary: Create multiple flows
   *     description: Creates multiple flows for a specific bot in a single request
   *     tags: [Flows]
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - in: path
   *         name: botId
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *         description: Bot unique identifier
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             properties:
   *               flows:
   *                 type: array
   *                 items:
   *                   $ref: '#/components/schemas/CreateFlowRequest'
   *     responses:
   *       201:
   *         description: Flows created successfully
   *         content:
   *           application/json:
   *             schema:
   *               allOf:
   *                 - $ref: '#/components/schemas/ApiResponse'
   *                 - type: object
   *                   properties:
   *                     data:
   *                       type: object
   *                       properties:
   *                         created:
   *                           type: array
   *                           items:
   *                             $ref: '#/components/schemas/Flow'
   *                         failed:
   *                           type: array
   *                           items:
   *                             type: object
   *                             properties:
   *                               request:
   *                                 $ref: '#/components/schemas/CreateFlowRequest'
   *                               error:
   *                                 type: string
   *       500:
   *         description: Internal server error
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ApiResponse'
   */
  async bulkCreateFlows(req: Request, res: Response): Promise<void> {
    try {
      const { botId } = req.params;
      const { flows } = req.body;
      const userId = (req as any).user?.id;

      // Ensure all flows have the correct botId
      const flowRequests = flows.map((flow: any) => ({
        ...flow,
        botId,
      }));

      const result = await this.flowService.bulkCreateFlows(flowRequests, userId);

      res.status(201).json({
        success: true,
        data: result,
        timestamp: new Date(),
      } as ApiResponse);
    } catch (error) {
      logger.error("Error in bulkCreateFlows controller:", error);
      res.status(500).json({
        success: false,
        error: {
          code: "INTERNAL_ERROR",
          message: "Failed to create flows",
        },
        timestamp: new Date(),
      } as ApiResponse);
    }
  }
}
