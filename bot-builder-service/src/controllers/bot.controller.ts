/**
 * Bot Controller
 *
 * Handles HTTP requests for bot management operations.
 *
 * @swagger
 * components:
 *   schemas:
 *     BotResponse:
 *       allOf:
 *         - $ref: '#/components/schemas/ApiResponse'
 *         - type: object
 *           properties:
 *             data:
 *               $ref: '#/components/schemas/Bot'
 *     BotsResponse:
 *       allOf:
 *         - $ref: '#/components/schemas/ApiResponse'
 *         - type: object
 *           properties:
 *             data:
 *               allOf:
 *                 - $ref: '#/components/schemas/PaginationResponse'
 *                 - type: object
 *                   properties:
 *                     items:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/Bot'
 *     BuildBotResponse:
 *       allOf:
 *         - $ref: '#/components/schemas/ApiResponse'
 *         - type: object
 *           properties:
 *             data:
 *               $ref: '#/components/schemas/BuildBotResult'
 */

import { Request, Response } from "express";
import { BotService } from "../services/bot.service";
import {
  CreateBotRequest,
  UpdateBotRequest,
  GetBotsRequest,
  CreateBotResponse,
  GetBotsResponse,
  BuildBotResponse,
} from "../types";
import { getPaginatedResults, logger, PaginationQuery } from "@neuratalk/common";
import { getTotalPages } from "../utils/api";
import { ApiResponse } from "@neuratalk/common";
import { Models } from "@neuratalk/bot-store";

export class BotController {
  private botService: BotService;

  constructor(botService: BotService, private models: Models) {
    this.botService = botService;
  }

  /**
   * @swagger
   * /api/v1/bots:
   *   post:
   *     summary: Create a new bot
   *     description: Creates a new chatbot with the specified configuration
   *     tags: [Bots]
   *     security:
   *       - BearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             $ref: '#/components/schemas/CreateBotRequest'
   *           example:
   *             name: "Customer Support Bot"
   *             description: "Handles customer inquiries and support requests"
   *             settings:
   *               nlu:
   *                 provider: "rasa"
   *                 confidenceThreshold: 0.7
   *     responses:
   *       201:
   *         description: Bot created successfully
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/BotResponse'
   *       400:
   *         description: Invalid request data
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ApiResponse'
   *       500:
   *         description: Internal server error
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ApiResponse'
   */
  async createBot(req: Request, res: Response): Promise<void> {
    try {
      const request: CreateBotRequest = req.body;
      const userId = (req as any).user?.id;

      const bot = await this.botService.createBot(request, userId);

      const response: CreateBotResponse = { bot };

      res.status(201).json({
        success: true,
        data: response,
        timestamp: new Date(),
      } as ApiResponse<CreateBotResponse>);
    } catch (error) {
      logger.error("Error in createBot controller:", error);
      res.status(500).json({
        success: false,
        error: {
          code: "INTERNAL_ERROR",
          message: "Failed to create bot",
        },
        timestamp: new Date(),
      } as ApiResponse);
    }
  }

  /**
   * @swagger
   * /api/v1/bots/{id}:
   *   get:
   *     summary: Get bot by ID
   *     description: Retrieves a specific bot by its unique identifier
   *     tags: [Bots]
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *         description: Bot unique identifier
   *         example: "123e4567-e89b-12d3-a456-************"
   *     responses:
   *       200:
   *         description: Bot retrieved successfully
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/BotResponse'
   *       404:
   *         description: Bot not found
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ApiResponse'
   *       500:
   *         description: Internal server error
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ApiResponse'
   */
  async getBotById(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;

      const bot = await this.botService.getBotById(id);

      if (!bot) {
        res.status(404).json({
          success: false,
          error: {
            code: "BOT_NOT_FOUND",
            message: "Bot not found",
          },
          timestamp: new Date(),
        } as ApiResponse);
        return;
      }

      res.json({
        success: true,
        data: bot,
        timestamp: new Date(),
      } as ApiResponse);
    } catch (error) {
      logger.error("Error in getBotById controller:", error);
      res.status(500).json({
        success: false,
        error: {
          code: "INTERNAL_ERROR",
          message: "Failed to get bot",
        },
        timestamp: new Date(),
      } as ApiResponse);
    }
  }

  /**
   * PUT /api/v1/bots/{id}
   * Update bot
   */
  async updateBot(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const request: UpdateBotRequest = req.body;
      const userId = (req as any).user?.id;

      const bot = await this.botService.updateBot(id, request, userId);

      if (!bot) {
        res.status(404).json({
          success: false,
          error: {
            code: "BOT_NOT_FOUND",
            message: "Bot not found",
          },
          timestamp: new Date(),
        } as ApiResponse);
        return;
      }

      res.json({
        success: true,
        data: bot,
        timestamp: new Date(),
      } as ApiResponse);
    } catch (error) {
      logger.error("Error in updateBot controller:", error);
      res.status(500).json({
        success: false,
        error: {
          code: "INTERNAL_ERROR",
          message: "Failed to update bot",
        },
        timestamp: new Date(),
      } as ApiResponse);
    }
  }

  /**
   * DELETE /api/v1/bots/{id}
   * Delete bot
   */
  async deleteBot(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;

      const deleted = await this.botService.deleteBot(id);

      if (!deleted) {
        res.status(404).json({
          success: false,
          error: {
            code: "BOT_NOT_FOUND",
            message: "Bot not found",
          },
          timestamp: new Date(),
        } as ApiResponse);
        return;
      }

      res.status(204).send();
    } catch (error) {
      logger.error("Error in deleteBot controller:", error);
      res.status(500).json({
        success: false,
        error: {
          code: "INTERNAL_ERROR",
          message: "Failed to delete bot",
        },
        timestamp: new Date(),
      } as ApiResponse);
    }
  }


   async getBots(req: Request<any, any, any, PaginationQuery>, res: Response): Promise<void> {
      try {
        const result = await getPaginatedResults(
          this.models.Bot,
          req.query as PaginationQuery,
          ["name"],
        );
  
        res.json({
          success: true,
          data: result,
          timestamp: new Date(),
        } as ApiResponse);
      } catch (error: any) {
        logger.error("Error fetching Bots:", error);
        res.status(500).json({
          success: false,
          error: {
            code: "INTERNAL_ERROR",
            message: "Failed to fetch Bots",
          },
          timestamp: new Date(),
        } as ApiResponse);
      }
    }

  /**
   * GET /api/v1/bots
   * Get bots with pagination and filtering
   */
  
  // async getBots(req: Request, res: Response): Promise<void> {
  //   try {
  //     const {
  //       page = 1,
  //       limit = 20,
  //       search,
  //       status,
  //       sortBy = "createdAt",
  //       sortOrder = "DESC",
  //       filterFrom,
  //       filterTo,
  //       filterDays,
  //       filterField = "updatedAt",
  //     } = req.query as any;

  //     let filterDaysInt: number | undefined;
  //     if (filterDays) {
  //       filterDaysInt = parseInt(filterDays, 10);
  //     }

  //     const { bots, total } = await this.botService.getBots(
  //       parseInt(page, 10),
  //       parseInt(limit, 10),
  //       search,
  //       status,
  //       sortBy,
  //       sortOrder,
  //       filterField,
  //       filterFrom,
  //       filterTo,
  //       filterDaysInt,
  //     );

  //     const totalPages = getTotalPages(total, parseInt(limit, 10));

  //     const response: GetBotsResponse = {
  //       items: bots,
  //       pagination: {
  //         page: parseInt(page, 10),
  //         limit: parseInt(limit, 10),
  //         total,
  //         totalPages,
  //         hasNext: parseInt(page, 10) < totalPages,
  //         hasPrev: parseInt(page, 10) > 1,
  //       },
  //     };

  //     res.json({
  //       success: true,
  //       data: response,
  //       timestamp: new Date(),
  //     } as ApiResponse<GetBotsResponse>);
  //   } catch (error) {
  //     logger.error("Error in getBots controller:", error);
  //     res.status(500).json({
  //       success: false,
  //       error: {
  //         code: "INTERNAL_ERROR",
  //         message: "Failed to get bots",
  //       },
  //       timestamp: new Date(),
  //     } as ApiResponse);
  //   }
  // }

  /**
   * POST /api/v1/bots/{id}/activate
   * Activate bot
   */
  async activateBot(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const userId = (req as any).user?.id;

      const bot = await this.botService.activateBot(id, userId);

      if (!bot) {
        res.status(404).json({
          success: false,
          error: {
            code: "BOT_NOT_FOUND",
            message: "Bot not found",
          },
          timestamp: new Date(),
        } as ApiResponse);
        return;
      }

      res.json({
        success: true,
        data: bot,
        timestamp: new Date(),
      } as ApiResponse);
    } catch (error) {
      logger.error("Error in activateBot controller:", error);
      res.status(500).json({
        success: false,
        error: {
          code: "INTERNAL_ERROR",
          message: "Failed to activate bot",
        },
        timestamp: new Date(),
      } as ApiResponse);
    }
  }

  /**
   * POST /api/v1/bots/{id}/deactivate
   * Deactivate bot
   */
  async deactivateBot(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const userId = (req as any).user?.id;

      const bot = await this.botService.deactivateBot(id, userId);

      if (!bot) {
        res.status(404).json({
          success: false,
          error: {
            code: "BOT_NOT_FOUND",
            message: "Bot not found",
          },
          timestamp: new Date(),
        } as ApiResponse);
        return;
      }

      res.json({
        success: true,
        data: bot,
        timestamp: new Date(),
      } as ApiResponse);
    } catch (error) {
      logger.error("Error in deactivateBot controller:", error);
      res.status(500).json({
        success: false,
        error: {
          code: "INTERNAL_ERROR",
          message: "Failed to deactivate bot",
        },
        timestamp: new Date(),
      } as ApiResponse);
    }
  }

  /**
   * @swagger
   * /api/v1/bots/{id}/build:
   *   post:
   *     summary: Build a Rasa bot
   *     description: Generates Rasa NLU files for the specified bot in the rasa-nlu directory
   *     tags: [Bots]
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *         description: Bot unique identifier
   *         example: "123e4567-e89b-12d3-a456-************"
   *     responses:
   *       200:
   *         description: Bot built successfully
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/BuildBotResponse'
   *       404:
   *         description: Bot not found
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ApiResponse'
   *       500:
   *         description: Internal server error
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ApiResponse'
   */
  async buildBot(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;

      // Build the bot
      const result = await this.botService.buildBot(id);

      res.json({
        success: true,
        data: result,
        timestamp: new Date(),
      } as ApiResponse<BuildBotResponse>);
    } catch (error: any) {
      logger.error("Error in buildBot controller:", error);

      // Handle not found error
      if (error.message && error.message.includes("not found")) {
        res.status(404).json({
          success: false,
          error: {
            code: "BOT_NOT_FOUND",
            message: "Bot not found",
          },
          timestamp: new Date(),
        } as ApiResponse);
        return;
      }

      res.status(500).json({
        success: false,
        error: {
          code: "INTERNAL_ERROR",
          message: "Failed to build bot",
        },
        timestamp: new Date(),
      } as ApiResponse);
    }
  }

  /**
   * @swagger
   * /api/v1/bots/{botId}/channels/{channelType}:
   *   get:
   *     summary: Get channel configuration
   *     parameters:
   *       - in: path
   *         name: botId
   *         required: true
   *         schema:
   *           type: string
   *       - in: path
   *         name: channelType
   *         required: true
   *         schema:
   *           type: string
   *           enum: [web, whatsapp, telegram, slack]
   *     responses:
   *       200:
   *         description: Channel configuration retrieved
   */
  async getChannelConfig(req: Request, res: Response): Promise<void> {
    try {
      const { botId, channelType } = req.params;
      const config = await this.botService.getChannelConfig(botId, channelType);

      res.json({
        success: true,
        data: config,
        timestamp: new Date(),
      } as ApiResponse);
    } catch (error) {
      logger.error('Error getting channel config:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'GET_CHANNEL_CONFIG_ERROR',
          message: 'Failed to get channel configuration',
        },
        timestamp: new Date(),
      } as ApiResponse);
    }
  }

  /**
   * @swagger
   * /api/v1/bots/{botId}/clone:
   *   post:
   *     summary: Clone a bot
   *     description: Creates a new bot as a clone of the specified bot
   *     tags: [Bots]
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - in: path
   *         name: botId
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *         description: Bot unique identifier
   *         example: "123e4567-e89b-12d3-a456-************"
   *     responses:
   *       200:
   *         description: Bot cloned successfully
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ApiResponse'
   *       404:
   *         description: Bot not found
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ApiResponse'
   *       500:
   *         description: Internal server error
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ApiResponse'
   */

  async cloneBot(req: Request, res: Response): Promise<void> {
    try {
      const { botId } = req.params;
      const userId = (req as any).user?.id;
      const bot = await this.botService.cloneBot(botId, userId);

      if (!bot) {
        res.status(404).json({
          success: false,
          error: {
            code: "BOT_NOT_FOUND",
            message: "Bot not found",
          },
          timestamp: new Date(),
        } as ApiResponse);
        return;
      }

      res.json({
        success: true,
        data: bot,
        timestamp: new Date(),
      } as ApiResponse);
    } catch (error) {
      logger.error("Error in cloneBot controller:", error);
      res.status(500).json({
        success: false,
        error: {
          code: "INTERNAL_ERROR",
          message: "Failed to clone bot",
        },
        timestamp: new Date(),
      } as ApiResponse);
    }
  }

  async createChannelIntegration(req: Request, res: Response): Promise<void> {
    try {
      const { botId } = req.params;
      const channelData = req.body;
      const integration = await this.botService.createChannelIntegration(botId, channelData);

      res.status(201).json({
        success: true,
        data: integration,
        timestamp: new Date(),
      } as ApiResponse);
    } catch (error) {
      logger.error('Error creating channel integration:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'CREATE_CHANNEL_ERROR',
          message: 'Failed to create channel integration',
        },
        timestamp: new Date(),
      } as ApiResponse);
    }
  }

  async updateChannelIntegration(req: Request, res: Response): Promise<void> {
    try {
      const { botId, channelId } = req.params;
      const updateData = req.body;
      const integration = await this.botService.updateChannelIntegration(botId, channelId, updateData);

      res.json({
        success: true,
        data: integration,
        timestamp: new Date(),
      } as ApiResponse);
    } catch (error) {
      logger.error('Error updating channel integration:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'UPDATE_CHANNEL_ERROR',
          message: 'Failed to update channel integration',
        },
        timestamp: new Date(),
      } as ApiResponse);
    }
  }
}
