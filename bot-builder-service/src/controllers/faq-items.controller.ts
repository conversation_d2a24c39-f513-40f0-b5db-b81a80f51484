import { Request, Request<PERSON><PERSON><PERSON>, Response } from "express";
import { Models } from "@neuratalk/bot-store";
import { ApiResponse, getPaginatedResults, PaginationQuery, UuidParams } from "@neuratalk/common";
import { CreateFaqItemRequest, UpdateFaqItemRequest } from "../schemas";
import { logger } from "@neuratalk/common";
import { WhereOptions } from "sequelize";

export class FaqItemsController {
  private models: Models;

  constructor(models: Models) {
    this.models = models;
  }

  /**
   * @swagger
   * /api/v1/faq-items:
   *   post:
   *     summary: Create FAQ item
   *     tags: [FAQ Items]
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - knowledgeUnitId
   *               - questions
   *               - answer
   *             properties:
   *               knowledgeUnitId:
   *                 type: string
   *                 format: uuid
   *               questions:
   *                 type: array
   *                 items:
   *                   type: string
   *               answer:
   *                 type: string
   *     responses:
   *       201:
   *         description: Created
   */
  async create(req: Request<any, any, CreateFaqItemRequest>, res: Response): Promise<void> {
    try {
      const userId = req.user?.id || "system"; //TODO: need to remove this fallback
      const faqItem = await this.models.FaqItems.create({
        ...req.body,
        createdBy: userId,
        updatedBy: userId,
      });

      logger.info(`FAQ item created: ${faqItem.id}`);
      res.status(201).json({
        success: true,
        data: faqItem,
        timestamp: new Date(),
      } as ApiResponse);
    } catch (error: any) {
      logger.error("Error creating FAQ item:", error);
      res.status(400).json({
        success: false,
        error: {
          code: "VALIDATION_ERROR",
          message: error.message,
        },
        timestamp: new Date(),
      } as ApiResponse);
    }
  }

  /**
   * @swagger
   * /api/v1/faq-items:
   *   get:
   *     summary: List FAQ items with filtering and pagination
   *     tags: [FAQ Items]
   *     parameters:
   *       - in: query
   *         name: page
   *         schema:
   *           type: integer
   *           minimum: 1
   *           default: 1
   *       - in: query
   *         name: limit
   *         schema:
   *           type: integer
   *           minimum: 1
   *           maximum: 100
   *           default: 20
   *       - in: query
   *         name: search
   *         schema:
   *           type: string
   *       - in: query
   *         name: filter
   *         schema:
   *           type: string
   *         example: '{"knowledgeUnitId":{"eq":"uuid"}}'
   *     responses:
   *       200:
   *         description: Success
   */
  getAll = async (req: Request<any, any, PaginationQuery>, res: Response) => {
    try {
      const result = await getPaginatedResults(this.models.FaqItems, req.query as PaginationQuery, [
        "answer",
      ]);

      res.json({
        success: true,
        data: result,
        timestamp: new Date(),
      } as ApiResponse);
    } catch (error: any) {
      logger.error("Error fetching FAQ items:", error);
      res.status(500).json({
        success: false,
        error: {
          code: "INTERNAL_ERROR",
          message: "Failed to fetch FAQ items",
        },
        timestamp: new Date(),
      } as ApiResponse);
    }
  };

  /**
   * @swagger
   * /api/v1/faq-items/{id}:
   *   get:
   *     summary: Get FAQ item by ID
   *     tags: [FAQ Items]
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *     responses:
   *       200:
   *         description: Success
   */
  async getById(req: Request<UuidParams>, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const faqItem = await this.models.FaqItems.findOne({
        where: { id, deletedAt: null } as WhereOptions,
      });

      if (!faqItem) {
        res.status(404).json({
          success: false,
          error: {
            code: "NOT_FOUND",
            message: "FAQ item not found",
          },
          timestamp: new Date(),
        } as ApiResponse);
        return;
      }

      res.json({
        success: true,
        data: faqItem,
        timestamp: new Date(),
      } as ApiResponse);
    } catch (error: any) {
      logger.error(`Error fetching FAQ item ${req.params.id}:`, error);
      res.status(500).json({
        success: false,
        error: {
          code: "INTERNAL_ERROR",
          message: "Failed to fetch FAQ item",
        },
        timestamp: new Date(),
      } as ApiResponse);
    }
  }

  /**
   * @swagger
   * /api/v1/faq-items/{id}:
   *   put:
   *     summary: Update FAQ item
   *     tags: [FAQ Items]
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *     responses:
   *       200:
   *         description: Updated
   */
  async update(req: Request<UuidParams, any, UpdateFaqItemRequest>, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const userId = req.user?.id || "system";

      const [updated] = await this.models.FaqItems.update(
        {
          ...req.body,
          updatedBy: userId,
        },
        {
          where: { id, deletedAt: null } as WhereOptions,
        },
      );

      if (!updated) {
        res.status(404).json({
          success: false,
          error: {
            code: "NOT_FOUND",
            message: "FAQ item not found",
          },
          timestamp: new Date(),
        } as ApiResponse);
        return;
      }

      const faqItem = await this.models.FaqItems.findByPk(id);
      logger.info(`FAQ item updated: ${id}`);

      res.json({
        success: true,
        data: faqItem,
        timestamp: new Date(),
      } as ApiResponse);
    } catch (error: any) {
      logger.error(`Error updating FAQ item ${req.params.id}:`, error);
      res.status(400).json({
        success: false,
        error: {
          code: "VALIDATION_ERROR",
          message: error.message,
        },
        timestamp: new Date(),
      } as ApiResponse);
    }
  }

  /**
   * @swagger
   * /api/v1/faq-items/{id}:
   *   delete:
   *     summary: Delete FAQ item
   *     tags: [FAQ Items]
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *     responses:
   *       204:
   *         description: Deleted
   */
  async delete(req: Request<UuidParams>, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const deleted = await this.models.FaqItems.destroy({ where: { id } });

      if (!deleted) {
        res.status(404).json({
          success: false,
          error: {
            code: "NOT_FOUND",
            message: "FAQ item not found",
          },
          timestamp: new Date(),
        } as ApiResponse);
        return;
      }

      logger.info(`FAQ item deleted: ${id}`);
      res.status(204).send();
    } catch (error: any) {
      logger.error(`Error deleting FAQ item ${req.params.id}:`, error);
      res.status(500).json({
        success: false,
        error: {
          code: "INTERNAL_ERROR",
          message: "Failed to delete FAQ item",
        },
        timestamp: new Date(),
      } as ApiResponse);
    }
  }
}
