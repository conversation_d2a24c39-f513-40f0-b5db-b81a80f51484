/**
 * Rasa Service
 *
 * Handles Rasa bot generation and related operations.
 */

import fs from "fs";
import path from "path";
import { promisify } from "util";
import { logger } from "@neuratalk/common";
import { Bot, BuildBotResponse } from "../types";
import { FlowService } from "./flow.service";

//TODO: need to rewrite the whole service to make it compatible with the new implementation

export class RasaService {
  private flowService: FlowService;

  constructor(flowService: FlowService) {
    this.flowService = flowService;
  }

  /**
   * Build a Rasa bot for the specified bot ID
   * Creates necessary files in the rasa-nlu directory
   */
  async buildBot(bot: Bot): Promise<BuildBotResponse> {
    try {
      const mkdir = promisify(fs.mkdir);
      const writeFile = promisify(fs.writeFile);

      // Create bot directory in rasa-nlu folder
      const botDir = path.join(process.cwd(), "..", "rasa-nlu", bot.id);

      try {
        await mkdir(botDir, { recursive: true });
        await mkdir(path.join(botDir, "data"), { recursive: true });
      } catch (err: any) {
        logger.error(`Error creating directories for bot ${bot.id}:`, err);
        throw new Error(`Failed to create bot directories: ${err.message}`);
      }

      // Get all flows for this bot to extract intents
      const flows = await this.flowService.getFlowsByBotId(bot.id);

      // Extract intents and examples from flows
      const intents = new Set<string>();
      const intentExamples = new Map<string, string[]>();
      const entities = new Set<string>();

      // Process flows to extract intents and examples
      flows.forEach((flow) => {
        if (flow?.nodes) {
          Object.values(flow.nodes).forEach((node: any) => {
            // Check if node is an intent node
            if (node.type === "intent" && node.intent) {
              const intentName = node.intent;
              intents.add(intentName);

              // Extract examples if available
              if (Array.isArray(node.examples)) {
                if (!intentExamples.has(intentName)) {
                  intentExamples.set(intentName, []);
                }
                intentExamples.get(intentName)?.push(...node.examples);
              }

              // Extract entities if available
              if (Array.isArray(node.entities)) {
                node.entities.forEach((entity: any) => entities.add(entity));
              }
            }
          });
        }
      });

      // Add fallback intent if no intents found
      if (intents.size === 0) {
        intents.add("nlu_fallback");
      }

      // Create config.yml
      const configYml = `# Rasa NLU Configuration for bot ${bot.name}
# Generated automatically by bot-builder-service

# The config recipe.
# https://rasa.com/docs/rasa/model-configuration/
# recipe: default.v1

language: en

pipeline:
  # Tokenization
  - name: WhitespaceTokenizer
  
  # Featurization
  - name: RegexFeaturizer
  - name: LexicalSyntacticFeaturizer
  - name: CountVectorsFeaturizer
  - name: CountVectorsFeaturizer
    analyzer: char_wb
    min_ngram: 1
    max_ngram: 4
  
  # Intent Classification
  - name: DIETClassifier
    epochs: 100
    constrain_similarities: true
    model_confidence: softmax
    
  # Entity Extraction
  - name: EntitySynonymMapper
  - name: ResponseSelector
    epochs: 100
    constrain_similarities: true
    
  # Fallback handling
  - name: FallbackClassifier
    threshold: 0.3
    ambiguity_threshold: 0.1


# Configuration for Rasa Core.
# https://rasa.com/docs/rasa/core/policies/
policies: []
# # No configuration for policies was provided. The following default policies were used to train your model.
# # If you'd like to customize them, uncomment and adjust the policies.
# # See https://rasa.com/docs/rasa/policies for more information.
#   - name: MemoizationPolicy
#   - name: RulePolicy
#   - name: UnexpecTEDIntentPolicy
#     max_history: 5
#     epochs: 100
#   - name: TEDPolicy
#     max_history: 5
#     epochs: 100

# Assistant ID
assistant_id: ${bot.id}
`;

      // Create domain.yml with intents from flows
      const intentsList = Array.from(intents)
        .map((intent) => `  - ${intent}`)
        .join("\n");
      const entitiesList = Array.from(entities)
        .map((entity) => `  - ${entity}`)
        .join("\n");

      const domainYml = `# Rasa Domain Configuration for bot ${bot.name}
# Generated automatically by bot-builder-service

version: "3.1"

# Intents that the NLU model can recognize
intents:
${intentsList}

# Entities that can be extracted
${
  entitiesList
    ? `entities:
${entitiesList || "  # No entities defined"}`
    : ""
}

# Session configuration
session_config:
  session_expiration_time: 60
  carry_over_slots_to_new_session: true
`;

      // Create nlu.yml with training examples from flows
      let nluContent = `# NLU Training Data for bot ${bot.name}
# Generated automatically by bot-builder-service

version: "3.1"

nlu:
`;

      // Add training examples for each intent
      for (const [intent, examples] of intentExamples.entries()) {
        nluContent += `- intent: ${intent}\n`;
        nluContent += "  examples: |\n";

        if (examples && examples.length > 0) {
          for (const example of examples) {
            nluContent += `    - ${example}\n`;
          }
        } else {
          // Add a placeholder example if none provided
          nluContent += `    - ${intent} example\n`;
        }
        nluContent += "\n";
      }

      // Add fallback intent if not already included
      if (!intentExamples.has("nlu_fallback")) {
        nluContent += `- intent: nlu_fallback
  examples: |
    - I don't understand
    - what?
    - huh?
    - can you repeat?
    - I'm confused
    - that doesn't make sense
`;
      }

      // Create endpoints.yml
      const endpointsYml = `# Rasa Endpoints Configuration
# This file contains the endpoints for external services

# Action server endpoint (not needed for NLU-only)
# action_endpoint:
#   url: "http://localhost:5055/webhook"

# Tracker store (for conversation tracking - not needed for NLU-only)
# tracker_store:
#   type: sql
#   dialect: "postgresql"
#   url: "postgresql://username:password@localhost:5432/rasa"
#   db: "rasa"

# Event broker (for publishing events - optional)
# event_broker:
#   type: pika
#   url: "localhost"
#   username: "guest"
#   password: "guest"
#   queue: "rasa_events"

# Model server (for serving models)
# model_server:
#   url: "http://localhost:5000"

# Lock store (for distributed deployments)
# lock_store:
#   type: "redis"
#   url: "localhost"
#   port: 6379
#   db: 1

`;

      // Write all files
      await writeFile(path.join(botDir, "config.yml"), configYml);
      await writeFile(path.join(botDir, "domain.yml"), domainYml);
      await writeFile(path.join(botDir, "data", "nlu.yml"), nluContent);
      await writeFile(path.join(botDir, "endpoints.yml"), endpointsYml);

      logger.info(`Bot ${bot.id} built successfully in ${botDir}`);

      return {
        botId: bot.id,
        buildPath: botDir,
        status: "success",
        timestamp: new Date(),
      };
    } catch (error) {
      logger.error(`Error building bot ${bot.id}:`, error);
      throw error;
    }
  }
}
