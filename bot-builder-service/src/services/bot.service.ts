/**
 * Bot Service
 *
 * Handles CRUD operations for bots and related business logic.
 */

import { BotSettings, CreateBotRequest, UpdateBotRequest, BuildBotResponse } from "../types";
import { v4 as uuidv4 } from "uuid";
import { getPaginatedResults, logger } from "@neuratalk/common";
import { RasaService } from "./rasa.service";
import { Bot, BotModelStatus, BotStatus, DatabaseConnection, Models } from "@neuratalk/bot-store";

export class BotService {
  private models: Models;
  private rasaService: RasaService;
  private db: DatabaseConnection;

  constructor(models: Models, rasaService: RasaService, db: DatabaseConnection) {
    this.models = models;
    this.rasaService = rasaService;
    this.db = db;
  }

  async createBot(request: CreateBotRequest, userId?: string): Promise<Bot> {
    try {
      // Default bot settings
      const defaultSettings: BotSettings = {
        nlu: {
          provider: "rasa",
          confidenceThreshold: 0.7,
          fallbackIntent: "nlu_fallback",
        },
        session: {
          ttlMinutes: 30,
          maxConcurrentSessions: 10,
          persistContext: true,
          enableSessionResumption: false,
        },
        messaging: {
          enableTypingIndicator: true,
          typingDelayMs: 1000,
          maxMessageLength: 4000,
          enableQuickReplies: true,
          enableRichMessages: true,
          defaultErrorMessage: "I apologize, but I encountered an error. Please try again.",
          defaultFallbackMessage: "I'm sorry, I didn't understand that. Could you please rephrase?",
        },
        execution: {
          maxExecutionTimeMs: 30000,
          maxLoopIterations: 100,
          enableAsyncOperations: true,
          asyncTimeoutMs: 30000,
          enableScriptExecution: true,
          scriptTimeoutMs: 100,
        },
        integrations: {
          analytics: { enabled: false },
          logging: {
            level: "info",
            enableChatHistory: true,
            retentionDays: 30,
          },
        },
        security: {
          enableRateLimit: true,
          rateLimitPerMinute: 60,
          enableInputValidation: true,
          allowedFileTypes: ["jpg", "jpeg", "png", "gif", "pdf", "doc", "docx"],
          maxFileSize: 10485760, // 10MB
          enableContentFilter: false,
        },
      };

      const settings = { ...defaultSettings, ...request.settings };

      const newBot = await this.models.Bot.create({
        id: uuidv4(),
        name: request.name,
        description: request.description,
        status: BotStatus.DRAFT,
        settings,
        metadata: request.metadata || {},
        createdBy: userId,
        updatedBy: userId,
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      logger.info(`Bot created: ${newBot.id} - ${newBot.name}`);
      return newBot.toJSON() as Bot;
    } catch (error) {
      logger.error("Error creating bot:", error);
      throw error;
    }
  }

  async getBotById(id: string): Promise<Bot | null> {
    try {
      const bot = await this.models.Bot.findByPk(id);
      return bot ? (bot.toJSON() as Bot) : null;
    } catch (error) {
      logger.error(`Error getting bot ${id}:`, error);
      throw error;
    }
  }

  async updateBot(id: string, request: UpdateBotRequest, userId?: string): Promise<Bot | null> {
    try {
      const bot = await this.models.Bot.findByPk(id);
      if (!bot) {
        return null;
      }

      const updateData: Partial<any> = {};

      if (request.name !== undefined) updateData.name = request.name;
      if (request.description !== undefined) updateData.description = request.description;
      if (request.settings !== undefined)
        updateData.settings = { ...bot.settings, ...request.settings };
      if (request.metadata !== undefined)
        updateData.metadata = { ...bot.metadata, ...request.metadata };
      if (userId) updateData.updatedBy = userId;
      if (request.domain !== undefined) updateData.domain = request.domain;

      if (Object.keys(updateData).length === 0) {
        return bot.toJSON() as Bot;
      }

      await bot.update(updateData);
      logger.info(`Bot updated: ${bot.id} - ${bot.name}`);
      return bot.toJSON() as Bot;
    } catch (error) {
      logger.error(`Error updating bot ${id}:`, error);
      throw error;
    }
  }

  async deleteBot(id: string): Promise<boolean> {
    try {
      // const result = await this.models.Bot.destroy({ where: { id } });
      const result = await this.models.Bot.update(
        { status: BotStatus.INACTIVE },
        { where: { id } },
      );
      const deleted = result[0] > 0;
      if (deleted) {
        logger.info(`Bot deleted: ${id}`);
      }
      return deleted;
    } catch (error) {
      logger.error(`Error deleting bot ${id}:`, error);
      throw error;
    }
  }

  async cloneBot(id: string, userId?: string): Promise<Bot> {
    try {
      const bot = await this.models.Bot.findByPk(id);
      if (!bot) {
        throw new Error(`Bot with ID ${id} not found`);
      }
      const newBot = await this.models.Bot.create({
        id: uuidv4(),
        name: bot.name,
        description: bot.description,
        status: BotStatus.DRAFT,
        settings: bot.settings,
        metadata: bot.metadata,
        createdBy: userId,
        updatedBy: userId,
        createdAt: new Date(),
        updatedAt: new Date(),
      });
      logger.info(`Bot cloned: ${newBot.id} - ${newBot.name}`);
      return newBot.toJSON() as Bot;
    } catch (error) {
      logger.error(`Error cloning bot ${id}:`, error);
      throw error;
    }
  }

  async getBots(
    page: number = 1,
    limit: number = 20,
    search?: string,
    status?: BotStatus,
    sortField?: string,
    sortOrder?: string,
    filterField?: string,
    filterFrom?: Date,
    filterTo?: Date,
    filterDays?: number,
  ): Promise<{ bots: Bot[]; total: number }> {
    try {
      const offset = (page - 1) * limit;

      const query: any = {};
      if (filterDays) {
        // where[filterField || "updatedAt"] = { [Op.gte]: new Date(new Date().getTime() - filterDays * 24 * 60 * 60 * 1000) };
        query["updatedAt"] = {
          gte: new Date(new Date().getTime() - filterDays * 24 * 60 * 60 * 1000),
        };
      }
      const where: any = {};
      const result = await getPaginatedResults(this.models, query, ["name", "description"]);

      // if (search) {
      //   where[Op.or] = [
      //     { name: { [Op.like]: `%${search}%` } },
      //     { description: { [Op.like]: `%${search}%` } },
      //   ];
      // }
      // if (status) {
      //   where.status = status;
      // }

      // if (filterFrom) {
      //   where[filterField || "updatedAt"] = { [Op.gte]: filterFrom };
      // }

      // if (filterTo) {
      //   where[filterField || "updatedAt"] = { [Op.lte]: filterTo };
      // }

      const { count, rows } = await this.models.Bot.findAndCountAll({
        where,
        limit,
        offset,
        order: [[sortField || "createdAt", sortOrder || "DESC"]],
      });

      return { bots: rows.map((r) => r.toJSON() as Bot), total: count };
    } catch (error) {
      logger.error("Error getting bots:", error);
      throw error;
    }
  }

  async activateBot(id: string, userId?: string): Promise<Bot | null> {
    return this.updateBotStatus(id, BotStatus.ACTIVE, userId);
  }

  async deactivateBot(id: string, userId?: string): Promise<Bot | null> {
    return this.updateBotStatus(id, BotStatus.INACTIVE, userId);
  }

  private async updateBotStatus(
    id: string,
    status: BotStatus,
    userId?: string,
  ): Promise<Bot | null> {
    try {
      const bot = await this.models.Bot.findByPk(id);
      if (!bot) {
        return null;
      }
      await bot.update({ status, updatedBy: userId });
      logger.info(`Bot status updated: ${bot.id} - ${status}`);
      return bot.toJSON() as Bot;
    } catch (error) {
      logger.error(`Error updating bot status ${id}:`, error);
      throw error;
    }
  }

  async buildBot(id: string): Promise<BuildBotResponse> {
    return this.db.transaction(async (transaction) => {
      try {
        const bot = await this.models.Bot.findByPk(id, { transaction });
        if (!bot) {
          throw new Error(`Bot with ID ${id} not found`);
        }

        const buildResult = await this.rasaService.buildBot(bot.toJSON() as Bot);

        const latestVersion = await this.models.BotModel.max("version", {
          where: { botId: id },
          transaction,
        });
        const newVersion = (typeof latestVersion === "number" ? latestVersion : 0) + 1;

        const newModel = await this.models.BotModel.create(
          {
            id: uuidv4(),
            botId: id,
            version: newVersion,
            status: BotModelStatus.SUCCEEDED,
            path: buildResult.buildPath,
            metadata: {
              source: "bot-builder-service",
            },
          },
          { transaction },
        );

        bot.previewModelId = newModel.id;
        bot.metadata = {
          ...bot.metadata,
          lastBuild: {
            timestamp: buildResult.timestamp.toISOString(),
            status: "success",
            modelId: newModel.id,
            version: newVersion,
          },
        };
        await bot.save({ transaction });

        logger.info(`Bot ${id} built successfully, model ${newModel.id} created.`);
        return {
          ...buildResult,
          modelId: newModel.id,
          version: newVersion,
        };
      } catch (error) {
        logger.error(`Error building bot ${id}:`, error);
        // The transaction will be rolled back automatically by Sequelize
        throw error;
      }
    });
  }

  async getChannelConfig(botId: string, channelType: string): Promise<any> {
    // Mock implementation
    return {
      channelType,
      isActive: true,
      config: {
        webhookUrl: `https://api.example.com/webhooks/${channelType}`,
        apiKey: "mock-api-key",
      },
    };
  }

  async createChannelIntegration(botId: string, channelData: any): Promise<any> {
    // Mock implementation
    return {
      id: "channel-" + Date.now(),
      botId,
      ...channelData,
      createdAt: new Date(),
    };
  }

  async updateChannelIntegration(botId: string, channelId: string, updateData: any): Promise<any> {
    // Mock implementation
    return {
      id: channelId,
      botId,
      ...updateData,
      updatedAt: new Date(),
    };
  }
}
