import { v4 as uuidv4 } from 'uuid';
import { DatabaseConnection, Models } from '@neuratalk/bot-store';
import { logger } from '@neuratalk/common';
import { TrainingJobStatus, TrainingResultStatus } from '../types/build-bot.type';
import { KafkaProducer } from '@neuratalk/common';



interface TrainingResult {
  status: TrainingResultStatus;
  modelUrl?: string;
  errorMessage?: string;
}

export class BuildService {
  private kafkaProducer: KafkaProducer;
  private models: Models;
  constructor(db: DatabaseConnection, kafkaProducer: KafkaProducer) {
    this.models = db.models;
    this.kafkaProducer = kafkaProducer;
  }

  async createBuild(botId: string): Promise<{ jobId: string }> {
    const jobId = uuidv4();

    // 1. Create a job record in the database
    await this.models.TrainingJob.create({
      id: jobId,
      botId: botId,
      status: TrainingJobStatus.PENDING,
      createdAt: new Date(),
      updatedAt: new Date(),
    });

    // 2. Send a message to <PERSON>f<PERSON> to trigger the training server
    const message = { botId, jobId };
    const record = {
      topic: 'training-requests',
      messages: [{ value: JSON.stringify(message) }],
    };
    await this.kafkaProducer.sendMessage(record);


    logger.info(`Build job ${jobId} created for bot ${botId} and queued via Kafka.`);
    
    return { jobId };
  }

  async updateBuildJob(jobId: string, result: TrainingResult): Promise<void> {
    logger.info(`Updating training job ${jobId} with status ${result.status}`);
    try {
      const job = await this.models.TrainingJob.findByPk(jobId);
      if (!job) {
        logger.error(`Training job with ID ${jobId} not found.`);
        return;
      }

      job.status = result.status;
      job.completedAt = new Date();
      if (result.status === TrainingResultStatus.COMPLETED && result.modelUrl) {
        job.modelUrl = result.modelUrl;
      }
      if (result.status === TrainingResultStatus.FAILED && result.errorMessage) {
        job.errorMessage = result.errorMessage;
      }

      await job.save();
      logger.info(`Successfully updated job ${jobId}.`);
    } catch (error) {
      logger.error(`Failed to update job ${jobId} in database:`, error);
    }
  }
}