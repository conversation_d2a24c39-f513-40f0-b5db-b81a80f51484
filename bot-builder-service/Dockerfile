# Check out https://hub.docker.com/_/node to select a new base image
FROM node:18-bookworm-slim
WORKDIR /app

COPY bot-builder-service/ ./bot-builder-service/
COPY packages/ ./packages/
COPY studio/ ./studio/

WORKDIR /app/studio

COPY install_studio_deps.sh install_studio_deps.sh
RUN chmod +x install_studio_deps.sh
RUN ./install_studio_deps.sh

WORKDIR /app/packages/bot-store
RUN npm install
RUN npm run build

WORKDIR /app/packages/common
RUN npm install
RUN npm run build

WORKDIR /app/studio/api_gw
RUN npm run build

WORKDIR /app/bot-builder-service
RUN npm install

# Run the application
CMD ["npm", "run", "dev"]
