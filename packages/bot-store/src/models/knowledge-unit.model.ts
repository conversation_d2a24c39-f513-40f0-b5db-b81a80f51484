import {
  DataTypes,
  Model,
  Optional,
  Sequelize,
  BelongsTo,
  HasMany,
  BelongsToGetAssociationMixin,
  HasManyGetAssociationsMixin,
  HasManyAddAssociationMixin,
  HasManyCreateAssociationMixin,
} from "sequelize";
import { BotModel } from "./bot.model";
import { FlowModel } from "./flow.model";
import { FaqItemsModel } from "./faq-items.model";
import { IntentItemsModel } from "./intent-items.model";
import { EntitiesModel } from "./entities.model";

export enum KnowledgeType {
  FAQ = "faq",
  INTENT = "intent",
  SMALL_TALK = "small_talk",
}

export interface KnowledgeUnitAttributes {
  id: string;
  botId: string;
  name: string;
  type: KnowledgeType;
  metadata: object;
  flowId?: string;
  createdAt: Date;
  updatedAt: Date;
  deletedAt?: Date;
  createdBy: string;
  updatedBy: string;
  deletedBy?: string;

  // Associations
  bot?: BotModel;
  flow?: FlowModel;
  faqItems?: FaqItemsModel[];
  intentItems?: IntentItemsModel[];
  entities?: EntitiesModel[];
}

interface KnowledgeUnitCreationAttributes
  extends Optional<
    KnowledgeUnitAttributes,
    "id" | "createdAt" | "updatedAt" | "deletedAt" | "deletedBy" | "flowId"
  > {}

export class KnowledgeUnitModel
  extends Model<KnowledgeUnitAttributes, KnowledgeUnitCreationAttributes>
  implements KnowledgeUnitAttributes
{
  public id!: string;
  public botId!: string;
  public name!: string;
  public type!: KnowledgeType;
  public metadata!: object;
  public flowId?: string;
  public createdAt!: Date;
  public updatedAt!: Date;
  public deletedAt?: Date;
  public createdBy!: string;
  public updatedBy!: string;
  public deletedBy?: string;

  // Mixins for associations
  public getBot!: BelongsToGetAssociationMixin<BotModel>;
  public getFlow!: BelongsToGetAssociationMixin<FlowModel>;
  public getFaqItems!: HasManyGetAssociationsMixin<FaqItemsModel>;
  public addFaqItem!: HasManyAddAssociationMixin<FaqItemsModel, string>;
  public createFaqItem!: HasManyCreateAssociationMixin<FaqItemsModel>;
  public getIntentItems!: HasManyGetAssociationsMixin<IntentItemsModel>;
  public addIntentItem!: HasManyAddAssociationMixin<IntentItemsModel, string>;
  public createIntentItem!: HasManyCreateAssociationMixin<IntentItemsModel>;
  public getEntities!: HasManyGetAssociationsMixin<EntitiesModel>;
  public addEntity!: HasManyAddAssociationMixin<EntitiesModel, string>;
  public createEntity!: HasManyCreateAssociationMixin<EntitiesModel>;

  // Properties for eager loading
  public bot?: BotModel;
  public flow?: FlowModel;
  public faqItems?: FaqItemsModel[];
  public intentItems?: IntentItemsModel[];
  public entities?: EntitiesModel[];

  public static associations: {
    bot: BelongsTo;
    flow: BelongsTo;
    faqItems: HasMany;
    intentItems: HasMany;
    entities: HasMany;
  };
}

export function initKnowledgeUnitModel(sequelize: Sequelize): typeof KnowledgeUnitModel {
  KnowledgeUnitModel.init(
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
      },
      botId: {
        type: DataTypes.UUID,
        allowNull: false,
      },
      name: {
        type: DataTypes.TEXT,
        allowNull: false,
      },
      flowId: {
        type: DataTypes.UUID,
        allowNull: true,
      },
      type: {
        type: DataTypes.ENUM(...Object.values(KnowledgeType)),
        allowNull: false,
      },
      metadata: {
        type: DataTypes.JSON,
        allowNull: false,
      },
      createdAt: {
        type: DataTypes.DATE,
        allowNull: false,
      },
      updatedAt: {
        type: DataTypes.DATE,
        allowNull: false,
      },
      deletedAt: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      createdBy: {
        type: DataTypes.UUID,
        allowNull: false,
      },
      updatedBy: {
        type: DataTypes.UUID,
        allowNull: false,
      },
      deletedBy: {
        type: DataTypes.UUID,
        allowNull: true,
      },
    },
    {
      sequelize,
      modelName: "KnowledgeUnitModel",
      tableName: "knowledge_units",
      paranoid: true,
    },
  );

  return KnowledgeUnitModel;
}
