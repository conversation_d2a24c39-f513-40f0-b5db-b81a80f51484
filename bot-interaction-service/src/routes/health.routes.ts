import { Router } from "express";
import { Health<PERSON>ontroller } from "../controllers/health.controller";
import { AppContext } from "../types/context";

export function createHealthRoutes(context: AppContext): Router {
  const router = Router();
  const healthController = new HealthController(
    context.redis,
    context.db,
    context.queue,
    context.nlu,
  );

  /**
   * GET /health
   * Comprehensive health check
   */
  router.get("/", healthController.healthCheck);

  /**
   * GET /health/ready
   * Readiness probe for Kubernetes
   */
  router.get("/ready", healthController.readinessCheck);

  /**
   * GET /health/live
   * Liveness probe for Kubernetes
   */
  router.get("/live", healthController.livenessCheck);

  /**
   * GET /health/metrics
   * Basic metrics endpoint
   */
  router.get("/metrics", healthController.metrics);

  return router;
}
