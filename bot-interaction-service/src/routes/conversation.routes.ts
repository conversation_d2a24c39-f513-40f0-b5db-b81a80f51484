import { Router } from "express";
import { ConversationController } from "../controllers/conversation.controller";
import { authMiddleware } from "../middleware/auth.middleware";
import { validateBody, validateParams } from "@neuratalk/common";
import { SendMessageSchema } from "../schemas";
import { UuidParamSchema } from "@neuratalk/common";
import { AppContext } from "../types/context";

export function createConversationRoutes(context: AppContext): Router {
  const router = Router();
  const conversationController = new ConversationController(context);

  /**
   * POST /api/v1/conversations/{id}/message
   * Send a message to a conversation
   */
  router.post(
    "/conversations/:id/message",
    validateParams(UuidParamSchema),
    validateBody(SendMessageSchema),
    conversationController.sendMessage,
  );

  /**
   * GET /api/v1/conversations/{id}/stream
   * Establish SSE connection for real-time updates
   */
  router.get(
    "/conversations/:id/stream",
    validateParams(UuidParamSchema),
    conversationController.streamConversation,
  );

  /**
   * GET /api/v1/conversations/{id}/context
   * Get conversation context (for debugging) - requires authentication
   */
  router.get(
    "/conversations/:id/context",
    authMiddleware,
    validateParams(UuidParamSchema),
    conversationController.getContext,
  );

  return router;
}
