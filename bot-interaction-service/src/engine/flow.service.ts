/**
 * Flow Service
 *
 * Implements cache-aside pattern for flow definitions.
 * First checks Redis cache, then falls back to database if cache miss.
 */

import { Bot, Flow } from "@neuratalk/bot-store";
import { DatabaseService } from "../services/database.service";
import { RedisService } from "../services/redis.service";
import { logger } from "@neuratalk/common";

export class FlowService {
  private databaseService: DatabaseService;
  private redisService: RedisService;
  private readonly FLOW_CACHE_TTL_MINUTES = 60; // Cache flows for 1 hour//TODO: don't use this instead use the sessionTimeout from

  constructor(databaseService: DatabaseService, redisService: RedisService) {
    this.databaseService = databaseService;
    this.redisService = redisService;
  }

  /**
   * Get bot node by ID and type using cache-aside pattern
   */
  async getBotNode(botId: string): Promise<Bot | null> {
    try {
      const cacheKey = `bot_node::${botId}`;
      // First, try to get from cache
      const cachedNode = await this.redisService.get(cacheKey);

      // if (cachedNode) {
      //   logger.info(`Bot ${botId} retrieved from cache`);
      //   return JSON.parse(cachedNode) as Bot;
      // }

      // Cache miss - get from database
      logger.info(`Bot ${botId} not in cache, fetching from database`);

      const botNode = await this.databaseService.getBot(botId);

      if (!botNode) throw new Error(`Bot ${botId} not found`);

      // Cache the node for future requests
      await this.redisService.set(
        cacheKey,
        JSON.stringify(botNode),
        this.FLOW_CACHE_TTL_MINUTES * 60,
      );
      logger.debug(`Bot ${botId} cached for ${this.FLOW_CACHE_TTL_MINUTES} minutes`);

      return botNode;
    } catch (error) {
      logger.error(`Error getting bot node ${botId}:`, error);
      return null;
    }
  }

  /**
   * Get flow by ID using cache-aside pattern
   */
  async getFlow(knowledgeUnitId: string): Promise<Flow | null> {
    try {
      // First, try to get from cache
      const cachedKnowledgeUnit = await this.redisService.getKnowledgeUnit(knowledgeUnitId);
      const cachedFlow = cachedKnowledgeUnit?.flow;

      // if (cachedFlow) {
      //   logger.debug(`knowledgeUnit Flow ${JSON.stringify(cachedFlow)} retrieved from cache`);
      //   return cachedFlow;
      // }

      // Cache miss - get from database
      logger.debug(`knowledgeUnit ${knowledgeUnitId} Flow not in cache, fetching from database`);
      const knowledgeUnit = await this.databaseService.getKnowledgeUnit(knowledgeUnitId);

      const flow = knowledgeUnit?.flow;

      if (flow) {
        // Cache the flow for future requests
        await this.redisService.setKnowledgeUnit(
          knowledgeUnitId,
          knowledgeUnit,
          this.FLOW_CACHE_TTL_MINUTES,
        );
        logger.debug(
          `KnowledgeUnit ${knowledgeUnitId} cached for ${this.FLOW_CACHE_TTL_MINUTES} minutes`,
        );
      }

      return flow ?? null;
    } catch (error) {
      logger.error(`Error getting flow ${knowledgeUnitId}:`, error);
      return null;
    }
  }

  /**
   * Get all flows for a bot
   */
  async getFlowsByBot(botId: string): Promise<Flow[]> {
    try {
      const cacheKey = `bot_flows:${botId}`;
      const cachedFlows = await this.redisService.get(cacheKey);

      if (cachedFlows) {
        logger.debug(`Bot flows for ${botId} retrieved from cache`);
        return JSON.parse(cachedFlows) as Flow[];
      }

      logger.debug(`Bot flows for ${botId} not in cache, fetching from database`);
      const flows = await this.databaseService.getFlowsByBot(botId);

      if (flows.length > 0) {
        // Cache the flows list for 30 minutes
        await this.redisService.set(
          cacheKey,
          JSON.stringify(flows),
          30 * 60, // 30 minutes in seconds //TODO: need to use bot session timeout here for every expiry
        );
        logger.debug(`Bot flows for ${botId} cached for 30 minutes`);
      }

      return flows;
    } catch (error) {
      logger.error(`Error getting flows for bot ${botId}:`, error);
      return [];
    }
  }

  /**
   * Invalidate flow cache (called when flow is updated)
   */
  async invalidateFlowCache(flowId: string, botId?: string): Promise<void> {
    try {
      // Invalidate specific flow cache
      await this.redisService.invalidateFlow(flowId);

      // If botId provided, also invalidate bot flows cache
      if (botId) {
        const cacheKey = `bot_flows:${botId}`;
        await this.redisService.del(cacheKey);
      }

      logger.info(`Flow cache invalidated for flow ${flowId}`);
    } catch (error) {
      logger.error(`Error invalidating flow cache for ${flowId}:`, error);
    }
  }

  /**
   * Preload flows into cache (useful for warming up cache)
   */
  async preloadFlows(flowIds: string[]): Promise<void> {
    try {
      const promises = flowIds.map(async (flowId) => {
        const flow = await this.databaseService.getFlow(flowId);
        if (flow) {
          await this.redisService.setFlow(flowId, flow, this.FLOW_CACHE_TTL_MINUTES);
        }
      });

      await Promise.all(promises);
      logger.info(`Preloaded ${flowIds.length} flows into cache`);
    } catch (error) {
      logger.error("Error preloading flows:", error);
    }
  }

  /**
   * Get flow statistics (cache hit rate, etc.)
   */
  getFlowCacheStats() {
    // This would require implementing counters in Redis
    // For now, return placeholder data
    return {
      totalRequests: 0,
      cacheHits: 0,
      cacheMisses: 0,
      hitRate: 0,
    };
  }
}
