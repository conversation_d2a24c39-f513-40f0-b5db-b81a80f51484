import {
  ConversationContext,
  OutgoingMessage,
  PreservedContext,
  JourneyResult,
  MessageOptions,
  JourneyContext,
  SendMessageRequest,
} from "../types";
import { RedisService } from "../services/redis.service";
import { DatabaseService } from "../services/database.service";
import { QueueService } from "../services/queue.service";
import { NLUService } from "../services/nlu.service";
import { FlowService } from "./flow.service";
import { logger } from "@neuratalk/common";
import { initializePlatform, getAppManModuleFunc } from "@studio/app-engine";
import { FlowNode, FormNodeType, MessageNodeType } from "../types/enum";

export interface FlowExecutionResult {
  success: boolean;
  messages: OutgoingMessage[];
  context: ConversationContext;
  shouldPause: boolean; // True for form inputs or async operations
  error?: string;
}

export class FlowEngine {
  private readonly flowService: FlowService;
  private appManModule?: ReturnType<typeof getAppManModuleFunc>;

  constructor(
    private readonly redisService: RedisService,
    databaseService: DatabaseService,
    queueService: QueueService,
    private readonly nluService: NLUService,
  ) {
    this.redisService = redisService;
    this.nluService = nluService;
    this.flowService = new FlowService(databaseService, redisService);

    this.initializeStudio();
  }

  initializeStudio() {
    const utilFunc = () => {
      if (!this.appManModule) {
        this.appManModule = getAppManModuleFunc();
      }
    };

    initializePlatform()
      .then(utilFunc)
      .catch((err) => {
        console.error(err);
      });
  }

  /**
   * Process a user message and execute the appropriate flow
   */
  async processMessage(
    conversationId: string,
    userRequestData: SendMessageRequest,
  ): Promise<FlowExecutionResult> {
    const { botId, channelType } = userRequestData;
    const options = {
      channelType,
    };
    try {
      logger.info(`Processing message for conversation ${conversationId}`);

      // await this.nluService.loadModel({ userRequestData.botId, modelPath });

      // Get or create conversation context
      let context = await this.redisService.getConversationContext(conversationId);

      context ??= await this.createNewContext(conversationId, userRequestData.botId, options);

      // Check if conversation is in a waiting state
      if (context.asyncOperationInProgress) {
        //TODO: might have to remove it
        logger.warn(
          `Conversation ${conversationId} has async operation in progress, ignoring message`,
        );
        return {
          success: false,
          messages: [],
          context,
          shouldPause: true,
          error: "Async operation in progress",
        };
      }

      // Determine which flow to execute
      const { flowId, journeyId } =
        (await this.determineFlow(context, userRequestData.content, botId)) ?? {};

      if (!flowId) {
        return this.handleFallback(context);
      }

      // Update context with current flow
      context.currentFlowId = flowId;
      context.currentJourneyId = journeyId;

      // Execute the flow
      return await this.studioExecuteFlow(context, userRequestData);
    } catch (error) {
      logger.error(`Error processing message for conversation ${conversationId}:`, error);

      return {
        success: false,
        messages: [
          {
            nodeType: FlowNode.MESSAGE,
            data: {
              text: "I apologize, but I encountered an error. Please try again.",
              type: MessageNodeType.TEXT,
            },
          },
        ],
        context:
          (await this.redisService.getConversationContext(conversationId)) ||
          (await this.createNewContext(conversationId, botId, options)), //TODO: need to check this
        shouldPause: false,
        error: error instanceof Error ? error.message : "Unknown error",
      };
    }
  }

  private async studioExecuteFlow(
    context: ConversationContext,
    userMessage: SendMessageRequest,
  ): Promise<FlowExecutionResult> {
    const messages: OutgoingMessage[] = [];
    let currentContext = { ...context };
    let shouldPause = false;
    let maxIterations = 1;
    let iterations = 0;

    try {
      while (iterations < maxIterations) {
        //TODO: update it accordingly with studio integration
        iterations++;
        const currentJourneyId = currentContext.currentJourneyId!;

        const preservedContext: PreservedContext = {
          currentFlowId: currentContext.currentFlowId!,
          currentJourneyId,
          preservedContext: currentContext.preservedContext,
        };

        const journeyContext: JourneyContext = {
          ...currentContext.journeyContext,
        };

        const { formData, currentForm, awaitingInput } = journeyContext;
        if (awaitingInput && currentForm) {
          const isSingleAskForm = currentForm?.formType === FormNodeType.SINGLE_ASK_FORM;

          let userFormData = {};
          if (isSingleAskForm) {
            userFormData = {
              [currentForm.prompt[0].fieldName]: userMessage.content,
            };
          } else {
            userFormData = userMessage.formData;
            // currentForm?.prompt.reduce((acc, prompt) => {
            //   if (prompt.fieldName) {
            //     acc[prompt.fieldName] = userMessage.formData[prompt.fieldName];//TODO
            //   }
            //   return acc;
            // }, {} as Record<string, string>)
          }
          journeyContext.formData = {
            ...formData,
            [currentForm.formId]: {
              ...formData?.[currentForm.formId],
              ...userFormData,
            },
          };
        }

        const journeyResult = await new Promise<JourneyResult>((resolve, reject) => {
          this.appManModule
            .flowEngineInterface(
              currentJourneyId,
              currentContext.userId ?? "external-user", //TODO: need to remove this value so userId will only be used
              {
                journeyContext,
              },
              resolve,
            )
            .catch(reject);
        });

        // Handle node execution result
        if (!journeyResult.success) {
          //TODO: need to add error handling
          logger.error(`Node execution failed: ${JSON.stringify(journeyResult.error)}`);
          break;
        }

        // Add any messages from node execution
        if (journeyResult.messages) {
          messages.push(...journeyResult.messages);
        }

        // Update context with any changes
        currentContext = { ...currentContext, journeyContext: journeyResult.context };

        // Check if execution should pause (form input, async operation)
        if (journeyResult.context.awaitingInput) {
          shouldPause = true;
          break;
        }

        if (journeyResult.context.flowAction) {
          const { flowAction } = journeyResult.context;
          if (flowAction.resumeFromThisContext) {
            preservedContext.journeyContext = journeyResult.context;
            currentContext.preservedContext = preservedContext;
          }
          currentContext.currentJourneyId = flowAction.journeyId;

          if (flowAction.passExistingContext) {
            currentContext.journeyContext = {
              completedForms: journeyResult.context.completedForms,
              formData: journeyResult.context.formData,
            };
          }

          maxIterations++;
          // const flowConnectorResult = await this.studioExecuteFlow(currentContext);

          // if (flowConnectorResult.success) {
          //   messages.push(...(flowConnectorResult.messages ?? []));
          // } else {
          //   logger.error(`Flow execution failed: ${flowConnectorResult.error}`);
          //   break;
          // }

          continue;

          // const currentPreservedContext = currentContext.preservedContext as any;
          // currentContext = { ...currentContext, ...(currentPreservedContext??{}) };
        }

        if (currentContext.preservedContext) {
          maxIterations++;
          const currentPreservedContext = currentContext.preservedContext;

          currentContext = { ...currentContext, ...currentPreservedContext };
        }
      }

      if (!shouldPause) {
        currentContext = {
          ...currentContext,
          currentFlowId: "",
          currentJourneyId: "",
          preservedContext: null,
          asyncOperationInProgress: false,
        };
      }

      // Save updated context
      await this.redisService.setConversationContext(currentContext);

      return {
        success: true,
        messages,
        context: currentContext,
        shouldPause,
      };
    } catch (error) {
      logger.error("Error executing flow:", error);

      return {
        success: false,
        messages,
        context: currentContext,
        shouldPause: false,
        error: error instanceof Error ? error.message : "Unknown error",
      };
    }
  }

  /**
   * Get value from context using dot notation (e.g., "context.user.name")
   */
  private getValueFromContext(path: string, context: ConversationContext): any {
    const parts = path.split(".");
    let current: any = context;

    for (const part of parts) {
      if (current && typeof current === "object" && part in current) {
        current = current[part];
      } else {
        return undefined;
      }
    }

    return current;
  }

  /**
   * Create a new conversation context
   */
  private async createNewContext(
    conversationId: string,
    botId: string,
    options: MessageOptions,
  ): Promise<ConversationContext> {
    const now = new Date();
    const bot = await this.flowService.getBotNode(botId);
    if (!bot) throw new Error("Bot not found!"); //TODO: add better message
    const modelPath = bot.trainingJobs?.[0].modelUrl
    const ttlMinutes = bot.settings?.session.ttlMinutes;
    const sessionTimeout = (ttlMinutes ?? 1) * 60;
    const context: ConversationContext = {
      chatConversationId: conversationId,
      botId,
      currentFlowId: "",
      journeyContext: {
        ...options,
        sessionTimeout,
      },
      metadata: {
        sessionTimeout,
      },
      sessionStartedAt: now,
      lastActivityAt: now,
      expiresAt: new Date(now.getTime() + sessionTimeout),
    };

    await this.nluService.loadModel({ botId, modelPath });

    await this.redisService.setConversationContext(context, ttlMinutes);
    return context;
  }

  /**
   * Determine which flow to execute based on user message
   */
  private async determineFlow(
    context: ConversationContext,
    userMessage: string,
    botId: string,
  ): Promise<{ flowId: string; journeyId: string } | null> {
    const botNode = await this.flowService.getBotNode(botId);
    //TODO: handle this funciton correclty optimise it
    // If already in a flow, continue with it
    if (context.currentFlowId && context.currentJourneyId) {
      return { flowId: context.currentFlowId, journeyId: context.currentJourneyId };
    }

    // Use NLU to determine intent and find matching flow
    const nluResponse = await this.nluService.parseMessage({
      text: userMessage,
      botId,
    });

    

    //TODO: remove this mock need to implement actual logic

    // const tempFlowMap = {
    //   greeting: "d6075eb6-5d8b-11f0-996c-78832945dc80",
    // };
    const flowId = nluResponse.intent.name.split("_")[1];

    // const flowId = tempFlowMap[nluResponse.intent.name];
    // Get flows for this bot and find one that matches the intent
    const flow = await this.flowService.getFlow(flowId);
    
    // const flows = await this.flowService.getFlowsByBot(botId);

    // for (const flow of flows) {
    //   for (const node of flow.knowledgeUnits) {
    //     if (node && node.type === KnowledgeType.INTENT && node.name === nluResponse.intent.name) {
    //       return flow.id;
    //     }
    //   }
    // }

    return flow?.id ? { flowId: flow.id, journeyId: flow.appId } : null;
  }

  /**
   * Handle fallback when no flow is found
   */
  private handleFallback(context: ConversationContext): FlowExecutionResult {
    const fallbackMessage: OutgoingMessage = {
      nodeType: FlowNode.MESSAGE,
      data: {
        text: "I'm sorry, I didn't understand that. Could you please rephrase your question?",
        type: MessageNodeType.TEXT,
      },
    };

    return {
      success: true,
      messages: [fallbackMessage],
      context,
      shouldPause: false,
    };
  }
}
