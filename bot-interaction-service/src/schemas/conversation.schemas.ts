import { z } from "zod";
import { UuidSchema } from "@neuratalk/common";
import { ChannelType } from "../types/conversation.types";

export const SendMessageSchema = z.object({
  botId: UuidSchema,
  content: z.string().min(1).max(4000),
  messageType: z.enum(["text", "image", "file"]).default("text"),
  channelType: z.nativeEnum(ChannelType).default(ChannelType.WEB),
  formData: z.record(z.any()).optional(),
  metadata: z.record(z.any()).optional(),
});

export const SendMessageParamSchema = z.object({
  id: UuidSchema,
});

export type SendMessageRequest = z.infer<typeof SendMessageSchema>;
export type SendMessageParam = z.infer<typeof SendMessageParamSchema>;
