/**
 * Database Service
 *
 * Handles PostgreSQL connections and read-only operations for flow definitions.
 * The bot-interaction-service has read-only access to the shared database.
 */

import { logger } from "@neuratalk/common";
import { Bot, DatabaseConnection, Flow, KnowledgeUnit, Models } from "@neuratalk/bot-store";

export class DatabaseService {
  private models: Models;
  constructor(private db: DatabaseConnection) {
    this.models = this.db.models;
  }
  // --- Flow Operations (Read-Only) ---

  getFlow = async (flowId: string): Promise<Flow | null> => {
    try {
      const flow = await this.models.Flow.findByPk(flowId);

      if (!flow) {
        logger.warn(`Flow with ID: ${flowId} not found or is inactive.`);
        return null;
      }

      return flow;
    } catch (error) {
      logger.error(`Error fetching flow ${flowId}:`, error);
      return null;
    }
  };

  getKnowledgeUnit = async (knowledgeUnitId: string): Promise<KnowledgeUnit | null> => {
    try {
      const knowledgeUnit = await this.models.KnowledgeUnit.findByPk(knowledgeUnitId, {
        include: [
          {
            model: this.models.Flow,
            as: "flow",
          },
        ],
      });

      if (!knowledgeUnit) {
        logger.warn(`Flow with knowledgeUnitID: ${knowledgeUnitId} not found or is inactive.`);
        return null;
      }

      return knowledgeUnit;
    } catch (error) {
      logger.error(`Error fetching flow knowledgeUnit ${knowledgeUnitId}:`, error);
      return null;
    }
  };

  async getFlowsByBot(botId: string): Promise<Flow[]> {
    try {
      const flows = await this.models.Flow.findAll({
        where: {
          botId: botId,
        },
        order: [["createdAt", "DESC"]],
      });

      return flows;
    } catch (error) {
      logger.error(`Error fetching flows for bot ${botId}:`, error);
      return [];
    }
  }

  // --- Bot Operations (Read-Only) ---

  async getBot(botId: string): Promise<Bot | null> {
    try {
      let bot = await this.models.Bot.findOne({
        where: {
          id: botId,
          status: "draft",
        },
        include: [
          {
            model: this.models.TrainingJob,
            as: "trainingJobs",
            where: {
              status: "COMPLETED",
            },
            order: [
              ["updatedAt", "DESC"],
            ],
            limit: 1,
          },
        ],
      });

      // const trainingJob = await this.models.TrainingJob.findOne({
      //   where: {
      //     botId: botId,
      //     status: 'COMPLETED',
      //   }
      // })
      // bot = bot?.toJSON() as any
      // bot['previewModel'] = trainingJob?.toJSON()

      if (!bot) {
        logger.warn(`Bot with ID: ${botId} not found or is inactive.`);
        return null;
      }

      return bot;
    } catch (error) {
      logger.error(`Error fetching bot ${botId}:`, error);
      return null;
    }
  }
}
