/**
 * Health Check Service
 * Monitors system health and dependencies
 */

import { Request, Response } from "express";
import { RedisService } from "../services/redis.service";
import { QueueService } from "../services/queue.service";
import { CircuitBreakerManager } from "../utils/circuit-breaker";
import { logger } from "@neuratalk/common";
import { DatabaseConnection } from "@neuratalk/bot-store";

export interface HealthStatus {
  status: "healthy" | "degraded" | "unhealthy";
  timestamp: string;
  uptime: number;
  version: string;
  dependencies: {
    [key: string]: {
      status: "healthy" | "unhealthy";
      responseTime?: number;
      error?: string;
      lastChecked: string;
    };
  };
  metrics: {
    memoryUsage: NodeJS.MemoryUsage;
    cpuUsage: NodeJS.CpuUsage;
    activeConnections: number;
    circuitBreakers: any;
  };
}

export class HealthCheckService {
  private redisService: RedisService;
  private queueService: QueueService;
  private startTime: number;

  constructor(
    private databaseConnection: DatabaseConnection,
    redisService: RedisService,
    queueService: QueueService,
  ) {
    this.databaseConnection = databaseConnection;
    this.redisService = redisService;
    this.queueService = queueService;
    this.startTime = Date.now();
  }

  async getHealthStatus(): Promise<HealthStatus> {
    const dependencies: HealthStatus["dependencies"] = {};
    let overallStatus: "healthy" | "degraded" | "unhealthy" = "healthy";

    // Check database
    try {
      const dbStart = Date.now();
      await this.databaseConnection.healthCheck();
      dependencies.database = {
        status: "healthy",
        responseTime: Date.now() - dbStart,
        lastChecked: new Date().toISOString(),
      };
    } catch (error) {
      dependencies.database = {
        status: "unhealthy",
        error: error instanceof Error ? error.message : "Unknown error",
        lastChecked: new Date().toISOString(),
      };
      overallStatus = "unhealthy";
    }

    // Check Redis
    try {
      const redisStart = Date.now();
      await this.redisService.healthCheck();
      dependencies.redis = {
        status: "healthy",
        responseTime: Date.now() - redisStart,
        lastChecked: new Date().toISOString(),
      };
    } catch (error) {
      dependencies.redis = {
        status: "unhealthy",
        error: error instanceof Error ? error.message : "Unknown error",
        lastChecked: new Date().toISOString(),
      };
      if (overallStatus === "healthy") overallStatus = "degraded";
    }

    // Check Message Queue
    try {
      const queueStart = Date.now();
      await this.queueService.healthCheck();
      dependencies.messageQueue = {
        status: "healthy",
        responseTime: Date.now() - queueStart,
        lastChecked: new Date().toISOString(),
      };
    } catch (error) {
      dependencies.messageQueue = {
        status: "unhealthy",
        error: error instanceof Error ? error.message : "Unknown error",
        lastChecked: new Date().toISOString(),
      };
      if (overallStatus === "healthy") overallStatus = "degraded";
    }

    // Check external services (NLU, etc.)
    try {
      const nluStart = Date.now();
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000);

      const response = await fetch(`${process.env.RASA_NLU_URL}/status`, {
        method: "GET",
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (response.ok) {
        dependencies.nluService = {
          status: "healthy",
          responseTime: Date.now() - nluStart,
          lastChecked: new Date().toISOString(),
        };
      } else {
        throw new Error(`HTTP ${response.status}`);
      }
    } catch (error) {
      dependencies.nluService = {
        status: "unhealthy",
        error: error instanceof Error ? error.message : "Unknown error",
        lastChecked: new Date().toISOString(),
      };
      if (overallStatus === "healthy") overallStatus = "degraded";
    }

    return {
      status: overallStatus,
      timestamp: new Date().toISOString(),
      uptime: Date.now() - this.startTime,
      version: process.env.npm_package_version || "1.0.0",
      dependencies,
      metrics: {
        memoryUsage: process.memoryUsage(),
        cpuUsage: process.cpuUsage(),
        activeConnections: this.getActiveConnections(),
        circuitBreakers: CircuitBreakerManager.getInstance().getAllStats(),
      },
    };
  }

  private getActiveConnections(): number {
    // This would need to be implemented based on your connection tracking
    return 0;
  }

  /**
   * Express middleware for health check endpoint
   */
  healthCheckHandler = async (req: Request, res: Response) => {
    try {
      const health = await this.getHealthStatus();

      const statusCode =
        health.status === "healthy" ? 200 : health.status === "degraded" ? 200 : 503;

      res.status(statusCode).json(health);
    } catch (error) {
      logger.error("Health check failed:", error);
      res.status(503).json({
        status: "unhealthy",
        timestamp: new Date().toISOString(),
        error: "Health check service unavailable",
      });
    }
  };

  /**
   * Readiness probe - checks if service is ready to accept traffic
   */
  readinessHandler = async (req: Request, res: Response) => {
    try {
      // Check critical dependencies only
      await this.databaseConnection.healthCheck();

      res.status(200).json({
        status: "ready",
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      res.status(503).json({
        status: "not ready",
        timestamp: new Date().toISOString(),
        error: error instanceof Error ? error.message : "Unknown error",
      });
    }
  };

  /**
   * Liveness probe - checks if service is alive
   */
  livenessHandler = (req: Request, res: Response) => {
    res.status(200).json({
      status: "alive",
      timestamp: new Date().toISOString(),
      uptime: Date.now() - this.startTime,
    });
  };
}
