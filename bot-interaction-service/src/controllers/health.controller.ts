/**
 * Health Check Controller
 *
 * Provides health check endpoints for monitoring service status and dependencies.
 *
 * @swagger
 * components:
 *   schemas:
 *     HealthCheckResponse:
 *       allOf:
 *         - $ref: '#/components/schemas/ApiResponse'
 *         - type: object
 *           properties:
 *             data:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   enum: [healthy, degraded, unhealthy]
 *                 timestamp:
 *                   type: string
 *                   format: date-time
 *                 version:
 *                   type: string
 *                 uptime:
 *                   type: number
 *                 dependencies:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       name:
 *                         type: string
 *                       status:
 *                         type: string
 *                         enum: [healthy, unhealthy]
 *                       responseTime:
 *                         type: number
 */

import { Request, Response } from "express";
import { RedisService } from "../services/redis.service";
import { QueueService } from "../services/queue.service";
import { NLUService } from "../services/nlu.service";
import { HealthCheckResponse, ApiResponse } from "@neuratalk/common";
import { logger } from "@neuratalk/common";
import config from "../config";
import { DatabaseConnection } from "@neuratalk/bot-store";

export class HealthController {
  private queueService: QueueService;
  private nluService: NLUService;
  private startTime: Date;

  constructor(
    private redisService: RedisService,
    private databaseConnection: DatabaseConnection,
    queueService: QueueService,
    nluService: NLUService,
  ) {
    this.queueService = queueService;
    this.nluService = nluService;
    this.startTime = new Date();
  }

  /**
   * @swagger
   * /health:
   *   get:
   *     summary: Comprehensive health check
   *     description: Checks the health of the service and all its dependencies
   *     tags: [Health]
   *     responses:
   *       200:
   *         description: Service is healthy or degraded
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/HealthCheckResponse'
   *       503:
   *         description: Service is unhealthy
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ApiResponse'
   */
  healthCheck = async (req: Request, res: Response): Promise<void> => {
    try {
      const uptime = Date.now() - this.startTime.getTime();

      // Check all dependencies
      const [redisHealth, dbHealth, queueHealth, nluHealth] = await Promise.all([
        this.redisService.healthCheck(),
        this.databaseConnection.healthCheck(),
        this.queueService.healthCheck(),
        this.nluService.healthCheck(),
      ]);

      const dependencies = [
        {
          name: "redis",
          status: redisHealth.status,
          responseTime: redisHealth.responseTime,
        },
        {
          name: "database",
          status: dbHealth.status,
          responseTime: dbHealth.responseTime,
        },
        {
          name: "message_queue",
          status: queueHealth.status,
          responseTime: queueHealth.responseTime,
        },
        {
          name: "nlu_service",
          status: nluHealth.status,
          responseTime: nluHealth.responseTime,
          error: nluHealth.error,
        },
      ];

      // Determine overall status
      const unhealthyDeps = dependencies.filter((dep) => dep.status === "unhealthy");
      const overallStatus =
        unhealthyDeps.length === 0
          ? "healthy"
          : unhealthyDeps.length < dependencies.length
            ? "degraded"
            : "unhealthy";

      const healthResponse: HealthCheckResponse = {
        status: overallStatus,
        timestamp: new Date(),
        version: process.env.npm_package_version || "1.0.0",
        uptime,
        dependencies,
      };

      const statusCode =
        overallStatus === "healthy" ? 200 : overallStatus === "degraded" ? 200 : 503;

      res.status(statusCode).json({
        success: overallStatus !== "unhealthy",
        data: healthResponse,
        timestamp: new Date(),
      } as ApiResponse<HealthCheckResponse>);
    } catch (error) {
      logger.error("Error in health check:", error);

      res.status(503).json({
        success: false,
        error: {
          code: "HEALTH_CHECK_ERROR",
          message: "Health check failed",
        },
        timestamp: new Date(),
      } as ApiResponse);
    }
  };

  /**
   * @swagger
   * /health/ready:
   *   get:
   *     summary: Readiness probe
   *     description: Checks if the service is ready to accept requests
   *     tags: [Health]
   *     responses:
   *       200:
   *         description: Service is ready
   *         content:
   *           application/json:
   *             schema:
   *               allOf:
   *                 - $ref: '#/components/schemas/ApiResponse'
   *                 - type: object
   *                   properties:
   *                     data:
   *                       type: object
   *                       properties:
   *                         status:
   *                           type: string
   *                           enum: [ready]
   *       503:
   *         description: Service is not ready
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ApiResponse'
   */
  readinessCheck = async (req: Request, res: Response): Promise<void> => {
    try {
      // Check critical dependencies only
      const [redisHealth, dbHealth] = await Promise.all([
        this.redisService.healthCheck(),
        this.databaseConnection.healthCheck(),
      ]);

      const isReady = redisHealth.status === "healthy" && dbHealth.status === "healthy";

      if (isReady) {
        res.status(200).json({
          success: true,
          data: { status: "ready" },
          timestamp: new Date(),
        } as ApiResponse);
      } else {
        res.status(503).json({
          success: false,
          error: {
            code: "NOT_READY",
            message: "Service is not ready",
          },
          timestamp: new Date(),
        } as ApiResponse);
      }
    } catch (error) {
      logger.error("Error in readiness check:", error);

      res.status(503).json({
        success: false,
        error: {
          code: "READINESS_CHECK_ERROR",
          message: "Readiness check failed",
        },
        timestamp: new Date(),
      } as ApiResponse);
    }
  };

  /**
   * @swagger
   * /health/live:
   *   get:
   *     summary: Liveness probe
   *     description: Checks if the service is running
   *     tags: [Health]
   *     responses:
   *       200:
   *         description: Service is alive
   *         content:
   *           application/json:
   *             schema:
   *               allOf:
   *                 - $ref: '#/components/schemas/ApiResponse'
   *                 - type: object
   *                   properties:
   *                     data:
   *                       type: object
   *                       properties:
   *                         status:
   *                           type: string
   *                           enum: [alive]
   *                         uptime:
   *                           type: number
   */
  livenessCheck = async (req: Request, res: Response): Promise<void> => {
    // Simple liveness check - just return 200 if the process is running
    res.status(200).json({
      success: true,
      data: {
        status: "alive",
        uptime: Date.now() - this.startTime.getTime(),
      },
      timestamp: new Date(),
    } as ApiResponse);
  };

  /**
   * @swagger
   * /health/metrics:
   *   get:
   *     summary: Service metrics
   *     description: Returns basic metrics about the service
   *     tags: [Health]
   *     responses:
   *       200:
   *         description: Metrics retrieved successfully
   *         content:
   *           application/json:
   *             schema:
   *               allOf:
   *                 - $ref: '#/components/schemas/ApiResponse'
   *                 - type: object
   *                   properties:
   *                     data:
   *                       type: object
   *                       properties:
   *                         uptime:
   *                           type: number
   *                         memory:
   *                           type: object
   *                         cpu:
   *                           type: object
   *                         environment:
   *                           type: string
   *       500:
   *         description: Failed to retrieve metrics
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ApiResponse'
   */
  metrics = (req: Request, res: Response): void => {
    try {
      const uptime = Date.now() - this.startTime.getTime();
      const memoryUsage = process.memoryUsage();

      const metrics = {
        uptime,
        memory: {
          rss: memoryUsage.rss,
          heapTotal: memoryUsage.heapTotal,
          heapUsed: memoryUsage.heapUsed,
          external: memoryUsage.external,
        },
        cpu: process.cpuUsage(),
        environment: config.server.env,
        nodeVersion: process.version,
        pid: process.pid,
      };

      res.json({
        success: true,
        data: metrics,
        timestamp: new Date(),
      } as ApiResponse);
    } catch (error) {
      logger.error("Error in metrics endpoint:", error);

      res.status(500).json({
        success: false,
        error: {
          code: "METRICS_ERROR",
          message: "Failed to retrieve metrics",
        },
        timestamp: new Date(),
      } as ApiResponse);
    }
  };
}
