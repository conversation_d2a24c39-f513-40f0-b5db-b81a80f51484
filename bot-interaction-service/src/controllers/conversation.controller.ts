/**
 * Conversation Controller
 *
 * Handles HTTP requests for conversation management and message processing.
 * Implements the main API endpoints for the bot-interaction-service.
 *
 * @swagger
 * components:
 *   schemas:
 *     ConversationResponse:
 *       allOf:
 *         - $ref: '#/components/schemas/ApiResponse'
 *         - type: object
 *           properties:
 *             data:
 *               $ref: '#/components/schemas/SendMessageResponse'
 *     StreamEvent:
 *       type: object
 *       properties:
 *         type:
 *           type: string
 *           enum: [connected, message, heartbeat]
 *         conversationId:
 *           type: string
 *         timestamp:
 *           type: string
 *           format: date-time
 *         message:
 *           $ref: '#/components/schemas/Message'
 */

import { Request, Response } from "express";
import { FlowEngine } from "../engine/flow-engine";
import { RedisService } from "../services/redis.service";
import { QueueService } from "../services/queue.service";
import { SendMessageResponse } from "../types";
import { SendMessageParam, SendMessageRequest } from "../schemas";
import { logger } from "@neuratalk/common";
import { ApiResponse } from "@neuratalk/common";

import { DatabaseService } from "../services/database.service";
import { AppContext } from "../types/context";

export class ConversationController {
  private flowEngine: FlowEngine;
  private redisService: RedisService;
  private queueService: QueueService;
  private databaseService: DatabaseService;

  constructor(context: AppContext) {
    this.redisService = context.redis;
    this.queueService = context.queue;
    this.databaseService = new DatabaseService(context.db);
    this.flowEngine = new FlowEngine(
      this.redisService,
      this.databaseService,
      this.queueService,
      context.nlu,
    );
  }

  /**
   * @swagger
   * /api/v1/conversations/{id}/message:
   *   post:
   *     summary: Send a message to a conversation
   *     description: Processes a user message through the conversation flow engine
   *     tags: [Conversations]
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *         description: Conversation unique identifier
   *         example: "conv-123e4567-e89b-12d3-a456-************"
   *       - in: header
   *         name: X-Bot-Id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *         description: Bot identifier for conversation context
   *         example: "bot-123e4567-e89b-12d3-a456-************"
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             $ref: '#/components/schemas/SendMessageRequest'
   *           example:
   *             content: "Hello, I need help with my order"
   *             messageType: "text"
   *             metadata:
   *               userId: "user123"
   *               channel: "web"
   *     responses:
   *       200:
   *         description: Message processed successfully
   *         content:
   *           application/json:
   *             schema:
   *               allOf:
   *                 - $ref: '#/components/schemas/ApiResponse'
   *                 - type: object
   *                   properties:
   *                     data:
   *                       $ref: '#/components/schemas/SendMessageResponse'
   *       400:
   *         description: Invalid request data
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ApiResponse'
   *       500:
   *         description: Flow execution error
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ApiResponse'
   */
  sendMessage = async (
    req: Request<SendMessageParam, any, SendMessageRequest>,
    res: Response,
  ): Promise<void> => {
    try {
      const conversationId = req.params.id;
      const { content, botId }: SendMessageRequest = req.body;

      if (!conversationId) {
        res.status(400).json({
          success: false,
          error: {
            code: "MISSING_CONVERSATION_ID",
            message: "Conversation ID is required",
          },
          timestamp: new Date(),
        } as ApiResponse);
        return;
      }

      if (!botId) {
        res.status(400).json({
          success: false,
          error: {
            code: "MISSING_BOT_ID",
            message: "Bot ID is required in X-Bot-Id header",
          },
          timestamp: new Date(),
        } as ApiResponse);
        return;
      }

      if (!content?.trim().length) {
        res.status(400).json({
          success: false,
          error: {
            code: "EMPTY_MESSAGE",
            message: "Message content cannot be empty",
          },
          timestamp: new Date(),
        } as ApiResponse);
        return;
      }

      logger.info(`Processing message from conversation ${conversationId}: "${content}"`);

      // Process the message through the flow engine
      const result = await this.flowEngine.processMessage(conversationId, req.body);

      if (!result.success) {
        logger.error(`Flow execution failed for conversation ${conversationId}: ${result.error}`);
        res.status(500).json({
          success: false,
          error: {
            code: "FLOW_EXECUTION_ERROR",
            message: result.error || "Failed to process message",
          },
          timestamp: new Date(),
        } as ApiResponse);
        return;
      }

      const response: SendMessageResponse = {
        conversationId,
        response: result.messages || [
          {
            conversationId,
            content: "Message processed successfully",
            messageType: "text",
          },
        ],
      };

      res.json({
        success: true,
        data: response,
        timestamp: new Date(),
      } as ApiResponse<SendMessageResponse>);
    } catch (error) {
      logger.error("Error in sendMessage controller:", error);
      res.status(500).json({
        success: false,
        error: {
          code: "INTERNAL_ERROR",
          message: "An internal error occurred while processing your message",
        },
        timestamp: new Date(),
      } as ApiResponse);
    }
  };

  /**
   * @swagger
   * /api/v1/conversations/{id}/stream:
   *   get:
   *     summary: Stream conversation updates
   *     description: Establishes a Server-Sent Events connection for real-time conversation updates
   *     tags: [Conversations]
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *         description: Conversation unique identifier
   *         example: "conv-123e4567-e89b-12d3-a456-************"
   *     responses:
   *       200:
   *         description: SSE connection established
   *         content:
   *           text/event-stream:
   *             schema:
   *               type: string
   *             example: |
   *               data: {"type":"connected","conversationId":"conv-123","timestamp":"2023-01-01T00:00:00Z"}
   *
   *               data: {"type":"message","message":{"id":"msg-123","content":"Hello!","sender":"bot"},"timestamp":"2023-01-01T00:00:01Z"}
   *       400:
   *         description: Invalid conversation ID
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ApiResponse'
   */
  streamConversation = (req: Request, res: Response): void => {
    const conversationId = req.params.id;

    if (!conversationId) {
      res.status(400).json({
        success: false,
        error: {
          code: "MISSING_CONVERSATION_ID",
          message: "Conversation ID is required",
        },
        timestamp: new Date(),
      } as ApiResponse);
      return;
    }

    // Set SSE headers
    res.writeHead(200, {
      "Content-Type": "text/event-stream",
      "Cache-Control": "no-cache",
      Connection: "keep-alive",
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Headers": "Cache-Control",
    });

    // Send initial connection event
    res.write(
      `data: ${JSON.stringify({
        type: "connected",
        conversationId,
        timestamp: new Date(),
      })}\n\n`,
    );

    // Keep connection alive with periodic heartbeat
    const heartbeat = setInterval(() => {
      res.write(
        `data: ${JSON.stringify({
          type: "heartbeat",
          timestamp: new Date(),
        })}\n\n`,
      );
    }, 30000); // 30 seconds

    // Clean up on client disconnect
    req.on("close", () => {
      clearInterval(heartbeat);
      logger.debug(`SSE connection closed for conversation ${conversationId}`);
    });

    // In a real implementation, you would subscribe to Redis pub/sub
    // or use WebSockets for real-time message delivery
    logger.info(`SSE connection established for conversation ${conversationId}`);
  };

  /**
   * @swagger
   * /api/internal/v1/resume-flow:
   *   post:
   *     summary: Resume flow execution
   *     description: Internal endpoint for resuming flows after asynchronous operations
   *     tags: [Internal]
   *     security:
   *       - ApiKeyAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             $ref: '#/components/schemas/ResumeFlowRequest'
   *           example:
   *             conversationId: "conv-123e4567-e89b-12d3-a456-************"
   *             nodeId: "node-123"
   *             responseData: { "result": "success", "data": { "orderId": "ORD-123" } }
   *             success: true
   *     responses:
   *       200:
   *         description: Flow resumed successfully
   *         content:
   *           application/json:
   *             schema:
   *               allOf:
   *                 - $ref: '#/components/schemas/ApiResponse'
   *                 - type: object
   *                   properties:
   *                     data:
   *                       $ref: '#/components/schemas/ResumeFlowResponse'
   *       400:
   *         description: Invalid request data
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ApiResponse'
   *       401:
   *         description: Unauthorized - invalid API key
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ApiResponse'
   *       500:
   *         description: Flow resumption error
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ApiResponse'
   */

  /**
   * @swagger
   * /api/v1/conversations/{id}/context:
   *   get:
   *     summary: Get conversation context
   *     description: Retrieves the current context for a conversation (for debugging purposes)
   *     tags: [Conversations]
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *         description: Conversation unique identifier
   *         example: "conv-123e4567-e89b-12d3-a456-************"
   *     responses:
   *       200:
   *         description: Conversation context retrieved successfully
   *         content:
   *           application/json:
   *             schema:
   *               allOf:
   *                 - $ref: '#/components/schemas/ApiResponse'
   *                 - type: object
   *                   properties:
   *                     data:
   *                       type: object
   *                       description: Conversation context object
   *       401:
   *         description: Unauthorized - authentication required
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ApiResponse'
   *       404:
   *         description: Context not found
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ApiResponse'
   *       500:
   *         description: Internal server error
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ApiResponse'
   */
  getContext = async (req: Request, res: Response): Promise<void> => {
    try {
      const conversationId = req.params.id;

      if (!conversationId) {
        res.status(400).json({
          success: false,
          error: {
            code: "MISSING_CONVERSATION_ID",
            message: "Conversation ID is required",
          },
          timestamp: new Date(),
        } as ApiResponse);
        return;
      }

      const context = await this.redisService.getConversationContext(conversationId);

      if (!context) {
        res.status(404).json({
          success: false,
          error: {
            code: "CONTEXT_NOT_FOUND",
            message: "Conversation context not found",
          },
          timestamp: new Date(),
        } as ApiResponse);
        return;
      }

      res.json({
        success: true,
        data: context,
        timestamp: new Date(),
      } as ApiResponse);
    } catch (error) {
      logger.error("Error in getContext controller:", error);
      res.status(500).json({
        success: false,
        error: {
          code: "INTERNAL_ERROR",
          message: "An internal error occurred while fetching context",
        },
        timestamp: new Date(),
      } as ApiResponse);
    }
  };
}
