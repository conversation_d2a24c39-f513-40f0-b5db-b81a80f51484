/**
 * Message and Communication Types
 *
 * These types define the structure of messages exchanged between users and bots,
 * as well as internal service communications.
 */

import { ConversationContext, ConvNode } from "./conversation.types";

// --- Message Types ---

export interface Message {
  id: string;
  conversationId: string;
  sender: "user" | "bot" | "system";
  content: string;
  messageType: "text" | "image" | "file" | "quick_reply" | "card" | "carousel" | "system";
  timestamp: Date;
  metadata?: MessageMetadata;
}

export interface MessageMetadata {
  nodeId?: string; // Which node generated this message
  flowId?: string; // Which flow this message belongs to
  intent?: string; // Detected intent (for user messages)
  confidence?: number; // NLU confidence score
  entities?: Entity[]; // Extracted entities
  quickReplies?: QuickReply[]; // Suggested quick replies
  attachments?: Attachment[]; // File attachments
  isRetry?: boolean; // Whether this is a retry message
  retryCount?: number; // Number of retries
}

export interface Entity {
  name: string;
  value: string;
  confidence: number;
  start: number; // Character position in text
  end: number;
}

export interface QuickReply {
  title: string;
  payload: string;
  imageUrl?: string;
}

export interface Attachment {
  type: "image" | "file" | "audio" | "video";
  url: string;
  filename?: string;
  size?: number;
  mimeType?: string;
}

// --- Rich Message Types ---

export interface CardMessage {
  title: string;
  subtitle?: string;
  imageUrl?: string;
  buttons?: Button[];
}

export interface CarouselMessage {
  cards: CardMessage[];
}

export interface Button {
  type: "postback" | "url" | "phone";
  title: string;
  payload?: string; // For postback buttons
  url?: string; // For URL buttons
  phoneNumber?: string; // For phone buttons
}

// --- API Message Formats ---

export interface IncomingMessage {
  conversationId: string;
  userId?: string;
  content: string;
  messageType?: "text" | "image" | "file";
  timestamp?: Date;
  metadata?: Record<string, any>;
}

export type OutgoingMessage = ConvNode;

export interface SendMessageResponse {
  conversationId: string;
  response: OutgoingMessage[];
}
// --- Server-Sent Events ---

export interface SSEMessage {
  type: "message" | "typing" | "error" | "session_expired" | "flow_completed";
  data: any;
  timestamp: Date;
  conversationId: string;
}

export interface TypingIndicator {
  conversationId: string;
  isTyping: boolean;
  duration?: number; // How long to show typing indicator
}

// --- Message Queue Types ---

export interface QueueMessage {
  id: string;
  type: "chat_log" | "async_request" | "webhook" | "notification";
  payload: any;
  conversationId: string;
  priority: "low" | "normal" | "high" | "urgent";
  timestamp: Date;
  retryCount?: number;
  maxRetries?: number;
  delayUntil?: Date;
}

export interface ChatLogMessage extends QueueMessage {
  type: "chat_log";
  payload: {
    message: Message;
    context?: Partial<ConversationContext>;
  };
}

export interface AsyncRequestMessage extends QueueMessage {
  type: "async_request";
  payload: {
    url: string;
    method: string;
    headers?: Record<string, string>;
    body?: any;
    responseMapping: Record<string, string>;
    nodeId: string;
    flowId: string;
  };
}

// --- NLU Integration ---

export interface NLURequest {
  text: string;
  botId: string;
  modelPath: string;
}

export interface NLUResponse {
  intent: {
    name: string;
    confidence: number;
  };
  entities: Entity[];
  text: string;
  conversationId: string;
}

// --- Conversation Summary (to avoid circular imports) ---

export interface ConversationSummary {
  id: string;
  userId?: string;
  botId: string;
  status: "active" | "inactive" | "completed" | "abandoned";
  startedAt: Date;
  lastMessageAt: Date;
  messageCount: number;
  lastMessage?: string;
  currentFlow?: string;
}
